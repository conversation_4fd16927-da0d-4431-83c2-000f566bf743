var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/gemini/test/route.js")
R.c("server/chunks/node_modules_next_c1636439._.js")
R.c("server/chunks/[root-of-the-server]__1f8ec28f._.js")
R.m("[project]/.next-internal/server/app/api/gemini/test/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/gemini/test/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/gemini/test/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
