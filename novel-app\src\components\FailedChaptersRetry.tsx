'use client';

import React, { useState } from 'react';

interface FailedChapter {
  chapterNumber: number;
  chapterTitle: string;
  error?: string;
  apiKeyUsed?: string;
  processingTime?: number;
  detailedError?: string;
  debugInfo?: any; // 新增调试信息
}

interface FailedChaptersRetryProps {
  jobId: string;
  failedChapters: FailedChapter[];
  rules: string;
  model?: string;
  onRetryStart?: () => void;
  onRetryComplete?: (success: boolean, message: string) => void;
}

export default function FailedChaptersRetry({
  jobId,
  failedChapters,
  rules,
  model = 'gemini-2.5-flash-lite',
  onRetryStart,
  onRetryComplete,
}: FailedChaptersRetryProps) {
  const [selectedChapters, setSelectedChapters] = useState<number[]>([]);
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryMessage, setRetryMessage] = useState('');

  const handleSelectAll = () => {
    if (selectedChapters.length === failedChapters.length) {
      setSelectedChapters([]);
    } else {
      setSelectedChapters(failedChapters.map(ch => ch.chapterNumber));
    }
  };

  const handleChapterToggle = (chapterNumber: number) => {
    setSelectedChapters(prev => 
      prev.includes(chapterNumber)
        ? prev.filter(num => num !== chapterNumber)
        : [...prev, chapterNumber]
    );
  };

  const handleRetry = async () => {
    if (selectedChapters.length === 0) {
      alert('请选择要重试的章节');
      return;
    }

    setIsRetrying(true);
    setRetryMessage('正在重试失败的章节...');
    
    if (onRetryStart) {
      onRetryStart();
    }

    try {
      const response = await fetch('/api/rewrite/retry', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jobId,
          chapterNumbers: selectedChapters,
          rules,
          model,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setRetryMessage(`重试任务已创建，正在处理 ${selectedChapters.length} 个章节...`);
        if (onRetryComplete) {
          onRetryComplete(true, data.data.message);
        }
      } else {
        setRetryMessage(`重试失败: ${data.error}`);
        if (onRetryComplete) {
          onRetryComplete(false, data.error);
        }
      }
    } catch (error) {
      const errorMessage = `重试请求失败: ${error instanceof Error ? error.message : '未知错误'}`;
      setRetryMessage(errorMessage);
      if (onRetryComplete) {
        onRetryComplete(false, errorMessage);
      }
    } finally {
      setIsRetrying(false);
    }
  };

  if (failedChapters.length === 0) {
    return null;
  }

  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-red-800">
          失败章节 ({failedChapters.length} 个)
        </h3>
        <div className="flex gap-2">
          <button
            onClick={handleSelectAll}
            className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded transition-colors"
          >
            {selectedChapters.length === failedChapters.length ? '取消全选' : '全选'}
          </button>
          <button
            onClick={handleRetry}
            disabled={isRetrying || selectedChapters.length === 0}
            className="px-4 py-1 text-sm bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded transition-colors"
          >
            {isRetrying ? '重试中...' : `重试选中章节 (${selectedChapters.length})`}
          </button>
        </div>
      </div>

      {retryMessage && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded text-blue-800">
          {retryMessage}
        </div>
      )}

      <div className="space-y-2 max-h-60 overflow-y-auto">
        {failedChapters.map((chapter) => (
          <div
            key={chapter.chapterNumber}
            className="flex items-start gap-3 p-3 bg-white border border-red-200 rounded"
          >
            <input
              type="checkbox"
              checked={selectedChapters.includes(chapter.chapterNumber)}
              onChange={() => handleChapterToggle(chapter.chapterNumber)}
              className="mt-1"
            />
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-gray-900">
                  第 {chapter.chapterNumber} 章
                </span>
                <span className="text-gray-600 truncate">
                  {chapter.chapterTitle}
                </span>
              </div>
              
              <div className="text-sm text-red-600 mb-1">
                错误: {chapter.error || '未知错误'}
              </div>
              
              {chapter.detailedError && (
                <details className="text-xs text-gray-500">
                  <summary className="cursor-pointer hover:text-gray-700">
                    详细错误信息
                  </summary>
                  <div className="mt-1 p-2 bg-gray-50 rounded border text-xs font-mono whitespace-pre-wrap">
                    {chapter.detailedError}
                  </div>
                </details>
              )}

              {chapter.debugInfo && (
                <details className="text-xs text-gray-500 mt-2">
                  <summary className="cursor-pointer hover:text-gray-700 text-blue-600">
                    🔍 调试信息 (发送给模型的内容)
                  </summary>
                  <div className="mt-2 space-y-2">
                    {/* 章节信息 */}
                    <div className="p-2 bg-blue-50 rounded border">
                      <div className="font-semibold text-blue-800 mb-1">章节信息:</div>
                      <div className="font-mono text-xs">
                        <div>章节号: {chapter.debugInfo.chapterInfo?.number}</div>
                        <div>标题: {chapter.debugInfo.chapterInfo?.title}</div>
                        <div>内容长度: {chapter.debugInfo.chapterInfo?.contentLength} 字符</div>
                        <div>使用模型: {chapter.debugInfo.chapterInfo?.model}</div>
                      </div>
                    </div>

                    {/* 请求信息 */}
                    <div className="p-2 bg-green-50 rounded border">
                      <div className="font-semibold text-green-800 mb-1">请求信息:</div>
                      <div className="font-mono text-xs">
                        <div>提示词长度: {chapter.debugInfo.requestInfo?.promptLength} 字符</div>
                        <div>API URL: {chapter.debugInfo.requestInfo?.apiUrl}</div>
                        <div>API Key: {chapter.debugInfo.requestInfo?.apiKeyName}</div>
                      </div>
                    </div>

                    {/* API响应信息 */}
                    <div className="p-2 bg-red-50 rounded border">
                      <div className="font-semibold text-red-800 mb-1">API响应信息:</div>
                      <div className="font-mono text-xs">
                        <div>HTTP状态: {chapter.debugInfo.responseInfo?.status} {chapter.debugInfo.responseInfo?.statusText}</div>
                        <div>有数据: {chapter.debugInfo.responseInfo?.hasData ? '是' : '否'}</div>
                        <div>数据字段: {chapter.debugInfo.responseInfo?.dataKeys?.join(', ')}</div>
                        <div>candidates: {chapter.debugInfo.responseInfo?.candidates ? JSON.stringify(chapter.debugInfo.responseInfo.candidates) : '无'}</div>
                        <div>candidates长度: {chapter.debugInfo.responseInfo?.candidatesLength || 0}</div>
                      </div>
                    </div>

                    {/* 完整API响应 */}
                    <details className="p-2 bg-gray-50 rounded border">
                      <summary className="cursor-pointer font-semibold text-gray-800">
                        完整API响应 (点击展开)
                      </summary>
                      <pre className="mt-2 text-xs overflow-x-auto whitespace-pre-wrap break-words">
                        {chapter.debugInfo.responseInfo?.fullResponse}
                      </pre>
                    </details>
                  </div>
                </details>
              )}
              
              <div className="flex gap-4 text-xs text-gray-500 mt-1">
                {chapter.apiKeyUsed && (
                  <span>API Key: {chapter.apiKeyUsed}</span>
                )}
                {chapter.processingTime && (
                  <span>处理时间: {chapter.processingTime}ms</span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <h4 className="font-medium text-yellow-800 mb-2">重试建议:</h4>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• 重试将使用更保守的策略，串行处理章节以避免API限制</li>
          <li>• 如果仍然失败，可能需要检查API key配额或调整改写规则</li>
          <li>• 建议在API使用量较低的时间段进行重试</li>
        </ul>
      </div>
    </div>
  );
}
