{"/_not-found/page": "app/_not-found/page.js", "/api/chapters/route": "app/api/chapters/route.js", "/api/characters/route": "app/api/characters/route.js", "/api/context/analyze/route": "app/api/context/analyze/route.js", "/api/context/chapter/route": "app/api/context/chapter/route.js", "/api/context/novel/route": "app/api/context/novel/route.js", "/api/gemini/stats/route": "app/api/gemini/stats/route.js", "/api/gemini/test/route": "app/api/gemini/test/route.js", "/api/jobs/route": "app/api/jobs/route.js", "/api/novels/route": "app/api/novels/route.js", "/api/presets/route": "app/api/presets/route.js", "/api/rewrite/diagnostics/route": "app/api/rewrite/diagnostics/route.js", "/api/rewrite/retry/route": "app/api/rewrite/retry/route.js", "/api/rewrite/route": "app/api/rewrite/route.js", "/context/page": "app/context/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/help/page": "app/help/page.js", "/page": "app/page.js"}