var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/rewrite/diagnostics/route.js")
R.c("server/chunks/node_modules_next_6aeb4d78._.js")
R.c("server/chunks/[root-of-the-server]__a91f5fd2._.js")
R.m("[project]/.next-internal/server/app/api/rewrite/diagnostics/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/rewrite/diagnostics/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/rewrite/diagnostics/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
