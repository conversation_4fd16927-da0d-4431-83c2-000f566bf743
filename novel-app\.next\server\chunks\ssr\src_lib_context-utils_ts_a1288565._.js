module.exports = [
"[project]/src/lib/context-utils.ts [app-ssr] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

// 上下文工具函数 - 服务端和客户端通用
__turbopack_context__.s([
    "getChapterContextServer",
    ()=>getChapterContextServer,
    "getChapterContextWindowServer",
    ()=>getChapterContextWindowServer,
    "getNovelContextServer",
    ()=>getNovelContextServer,
    "rewriteChaptersWithContext",
    ()=>rewriteChaptersWithContext,
    "rewriteTextWithContextServer",
    ()=>rewriteTextWithContextServer
]);
// 重新导出客户端函数，提供统一接口
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/context-client.ts [app-ssr] (ecmascript)");
// 检查是否在服务端环境
function isServerSide() {
    return "undefined" === 'undefined';
}
async function rewriteTextWithContextServer(novelId, chapterNumber, originalText, rules, chapterTitle, model) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    try {
        // 导入服务端模块
        const { rewriteText } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-ssr] (ecmascript, async loader)");
        const { novelContextDb, chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-ssr] (ecmascript, async loader)");
        // 获取小说整体上下文
        const novelContext = novelContextDb.getByNovelId(novelId);
        // 获取章节上下文
        const chapterContext1 = chapterContextDb.getByChapter(novelId, chapterNumber);
        // 构建请求
        const request = {
            originalText,
            rules,
            chapterTitle,
            chapterNumber,
            model,
            novelContext: novelContext ? {
                summary: novelContext.summary,
                mainCharacters: novelContext.mainCharacters,
                worldSetting: novelContext.worldSetting,
                writingStyle: novelContext.writingStyle,
                tone: novelContext.tone
            } : undefined,
            chapterContext: chapterContext1 ? {
                previousChapterSummary: chapterContext1.previousChapterSummary,
                keyEvents: chapterContext1.keyEvents,
                characterStates: chapterContext1.characterStates,
                plotProgress: chapterContext1.plotProgress,
                contextualNotes: chapterContext1.contextualNotes
            } : undefined
        };
        return await rewriteText(request);
    } catch (error) {
        console.error('带上下文重写失败:', error);
        // 如果获取上下文失败，回退到普通重写
        const { rewriteText } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-ssr] (ecmascript, async loader)");
        return await rewriteText({
            originalText,
            rules,
            chapterTitle,
            chapterNumber,
            model
        });
    }
}
async function getNovelContextServer(novelId) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    const { novelContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-ssr] (ecmascript, async loader)");
    return novelContextDb.getByNovelId(novelId);
}
async function getChapterContextServer(novelId, chapterNumber) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    const { chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-ssr] (ecmascript, async loader)");
    return chapterContextDb.getByChapter(novelId, chapterNumber);
}
async function getChapterContextWindowServer(novelId, chapterNumber, windowSize = 2) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    const { chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-ssr] (ecmascript, async loader)");
    return chapterContextDb.getContextWindow(novelId, chapterNumber, windowSize);
}
async function rewriteChaptersWithContext(novelId, chapters, rules, onProgress, onChapterComplete, concurrency = 3, model = 'gemini-2.5-flash-lite', enableFailureRecovery = true) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    try {
        // 导入所需模块
        const { novelContextDb, chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-ssr] (ecmascript, async loader)");
        const { rewriteText } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-ssr] (ecmascript, async loader)");
        // 获取小说整体上下文
        const novelContext = novelContextDb.getByNovelId(novelId);
        const results = new Array(chapters.length);
        let completed = 0;
        let totalTokensUsed = 0;
        const startTime = Date.now();
        // 使用信号量控制并发
        const semaphore = {
            count: concurrency,
            waiting: []
        };
        const acquire = ()=>new Promise((resolve)=>{
                if (semaphore.count > 0) {
                    semaphore.count--;
                    resolve();
                } else {
                    semaphore.waiting.push(resolve);
                }
            });
        const release = ()=>{
            semaphore.count++;
            if (semaphore.waiting.length > 0) {
                const next = semaphore.waiting.shift();
                if (next) {
                    semaphore.count--;
                    next();
                }
            }
        };
        const processChapter = async (chapter, index)=>{
            await acquire();
            const chapterStartTime = Date.now();
            try {
                // 获取章节上下文
                const chapterContext1 = chapterContextDb.getByChapter(novelId, chapter.number);
                // 构建带上下文的请求
                const request = {
                    originalText: chapter.content,
                    rules,
                    chapterTitle: chapter.title,
                    chapterNumber: chapter.number,
                    model,
                    novelContext: novelContext ? {
                        summary: novelContext.summary,
                        mainCharacters: novelContext.mainCharacters,
                        worldSetting: novelContext.worldSetting,
                        writingStyle: novelContext.writingStyle,
                        tone: novelContext.tone
                    } : undefined,
                    chapterContext: chapterContext1 ? {
                        previousChapterSummary: chapterContext1.previousChapterSummary,
                        keyEvents: chapterContext1.keyEvents,
                        characterStates: chapterContext1.characterStates,
                        plotProgress: chapterContext1.plotProgress,
                        contextualNotes: chapterContext1.contextualNotes
                    } : undefined
                };
                const result = await rewriteText(request);
                const chapterProcessingTime = Date.now() - chapterStartTime;
                if (result.tokensUsed) {
                    totalTokensUsed += result.tokensUsed;
                }
                const chapterResult = {
                    success: result.success,
                    content: result.rewrittenText,
                    error: result.error,
                    details: {
                        apiKeyUsed: result.apiKeyUsed,
                        tokensUsed: result.tokensUsed,
                        model: result.model,
                        processingTime: chapterProcessingTime,
                        chapterNumber: chapter.number,
                        chapterTitle: chapter.title,
                        hasContext: !!(novelContext || chapterContext1),
                        detailedError: result.detailedError,
                        debugInfo: result.debugInfo
                    }
                };
                results[index] = chapterResult;
                completed++;
                // 实时回调章节完成
                if (onChapterComplete) {
                    onChapterComplete(index, chapterResult);
                }
                // 更新进度
                const progress = completed / chapters.length * 100;
                const totalTime = Date.now() - startTime;
                const averageTimePerChapter = totalTime / completed;
                if (onProgress) {
                    onProgress(progress, chapter.number, {
                        completed,
                        totalTokensUsed,
                        totalTime,
                        averageTimePerChapter,
                        hasContext: !!(novelContext || chapterContext1)
                    });
                }
                console.log(`第 ${chapter.number} 章重写${result.success ? '成功' : '失败'}${novelContext || chapterContext1 ? '（使用上下文）' : ''}: ${result.error || '完成'}`);
            } catch (error) {
                const chapterProcessingTime = Date.now() - chapterStartTime;
                const chapterResult = {
                    success: false,
                    content: '',
                    error: `重写异常: ${error instanceof Error ? error.message : '未知错误'}`,
                    details: {
                        processingTime: chapterProcessingTime,
                        chapterNumber: chapter.number,
                        chapterTitle: chapter.title,
                        hasContext: !!(novelContext || chapterContext)
                    }
                };
                results[index] = chapterResult;
                completed++;
                if (onChapterComplete) {
                    onChapterComplete(index, chapterResult);
                }
                console.error(`第 ${chapter.number} 章重写异常:`, error);
            } finally{
                release();
            }
        };
        // 并行处理所有章节
        const promises = chapters.map((chapter, index)=>processChapter(chapter, index));
        await Promise.all(promises);
        return results;
    } catch (error) {
        console.error('批量重写失败，回退到普通重写:', error);
        // 如果上下文重写失败，回退到普通重写
        const { rewriteChapters } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-ssr] (ecmascript, async loader)");
        return await rewriteChapters(chapters, rules, onProgress, onChapterComplete, concurrency, model, enableFailureRecovery);
    }
}
;
}),
"[project]/src/lib/context-utils.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "analyzeNovelClient",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["analyzeNovelClient"],
    "getChapterContext",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getChapterContext"],
    "getChapterContextClient",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getChapterContextClient"],
    "getChapterContextServer",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getChapterContextServer"],
    "getChapterContextWindow",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getChapterContextWindow"],
    "getChapterContextWindowClient",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getChapterContextWindowClient"],
    "getChapterContextWindowServer",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getChapterContextWindowServer"],
    "getNovelContext",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNovelContext"],
    "getNovelContextClient",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNovelContextClient"],
    "getNovelContextServer",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getNovelContextServer"],
    "rewriteChaptersWithContext",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["rewriteChaptersWithContext"],
    "rewriteTextWithContextClient",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["rewriteTextWithContextClient"],
    "rewriteTextWithContextServer",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["rewriteTextWithContextServer"]
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/context-utils.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/context-client.ts [app-ssr] (ecmascript)");
}),
];

//# sourceMappingURL=src_lib_context-utils_ts_a1288565._.js.map