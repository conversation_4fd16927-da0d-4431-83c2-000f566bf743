{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/database.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport crypto from 'crypto';\n\n// 数据类型定义\nexport interface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n}\n\nexport interface Chapter {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  title: string;\n  content: string;\n  filename: string;\n  createdAt: string;\n}\n\nexport interface RewriteRule {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string; // 角色类型：男主、女主、配角、反派、其他\n  description: string;\n  personality?: string; // 性格特点\n  appearance?: string; // 外貌描述\n  relationships?: string; // 人物关系\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 小说整体上下文\nexport interface NovelContext {\n  id: string;\n  novelId: string;\n  summary: string; // 小说整体摘要\n  mainCharacters: Array<{\n    name: string;\n    role: string;\n    description: string;\n    relationships?: string;\n  }>; // 主要人物信息\n  worldSetting: string; // 世界观设定\n  writingStyle: string; // 写作风格特征\n  mainPlotlines: string[]; // 主要情节线\n  themes: string[]; // 主题\n  tone: string; // 语调风格\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 章节上下文\nexport interface ChapterContext {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  keyEvents: string[]; // 关键事件\n  characterStates: Array<{\n    name: string;\n    status: string; // 人物在本章的状态\n    emotions: string; // 情感状态\n    relationships: string; // 关系变化\n  }>; // 人物状态\n  plotProgress: string; // 情节推进要点\n  previousChapterSummary?: string; // 前一章摘要\n  nextChapterHints?: string; // 对下一章的暗示\n  contextualNotes: string; // 上下文注释\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Preset {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RewriteJob {\n  id: string;\n  novelId: string;\n  chapters: number[];\n  ruleId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  // 新增详细信息字段\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    averageTimePerChapter: number;\n    apiKeyStats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n    }>;\n    chapterResults: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n      completedAt?: string;\n    }>;\n    model?: string;\n    concurrency?: number;\n  };\n}\n\n// 数据存储路径\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst NOVELS_FILE = path.join(DATA_DIR, 'novels.json');\nconst CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');\nconst RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');\nconst JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');\nconst CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');\nconst PRESETS_FILE = path.join(DATA_DIR, 'presets.json');\nconst NOVEL_CONTEXTS_FILE = path.join(DATA_DIR, 'novel-contexts.json');\nconst CHAPTER_CONTEXTS_FILE = path.join(DATA_DIR, 'chapter-contexts.json');\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  if (!fs.existsSync(DATA_DIR)) {\n    fs.mkdirSync(DATA_DIR, { recursive: true });\n  }\n}\n\n// 读取JSON文件\nfunction readJsonFile<T>(filePath: string): T[] {\n  ensureDataDir();\n  if (!fs.existsSync(filePath)) {\n    return [];\n  }\n  try {\n    const data = fs.readFileSync(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filePath}:`, error);\n    return [];\n  }\n}\n\n// 写入JSON文件\nfunction writeJsonFile<T>(filePath: string, data: T[]) {\n  ensureDataDir();\n  try {\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');\n  } catch (error) {\n    console.error(`Error writing ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substring(2);\n}\n\n// 基于内容生成确定性ID\nfunction generateDeterministicId(content: string): string {\n  return crypto.createHash('md5').update(content).digest('hex').substring(0, 18);\n}\n\n// 小说相关操作\nexport const novelDb = {\n  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),\n\n  getById: (id: string): Novel | undefined => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    return novels.find(novel => novel.id === id);\n  },\n\n  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n\n    // 使用书名生成确定性ID\n    const novelId = generateDeterministicId(novel.title);\n\n    // 检查是否已存在相同ID的小说\n    const existingNovel = novels.find(n => n.id === novelId);\n    if (existingNovel) {\n      // 如果已存在，更新现有记录\n      existingNovel.filename = novel.filename;\n      existingNovel.chapterCount = novel.chapterCount;\n      writeJsonFile(NOVELS_FILE, novels);\n      return existingNovel;\n    }\n\n    const newNovel: Novel = {\n      ...novel,\n      id: novelId,\n      createdAt: new Date().toISOString(),\n    };\n    novels.push(newNovel);\n    writeJsonFile(NOVELS_FILE, novels);\n    return newNovel;\n  },\n\n  update: (id: string, updates: Partial<Novel>): Novel | null => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return null;\n\n    novels[index] = { ...novels[index], ...updates };\n    writeJsonFile(NOVELS_FILE, novels);\n    return novels[index];\n  },\n\n  delete: (id: string): boolean => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return false;\n\n    novels.splice(index, 1);\n    writeJsonFile(NOVELS_FILE, novels);\n    return true;\n  }\n};\n\n// 章节相关操作\nexport const chapterDb = {\n  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),\n\n  getByNovelId: (novelId: string): Chapter[] => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.filter(chapter => chapter.novelId === novelId);\n  },\n\n  getById: (id: string): Chapter | undefined => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.find(chapter => chapter.id === id);\n  },\n\n  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapter: Chapter = {\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    chapters.push(newChapter);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return newChapter;\n  },\n\n  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {\n    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapters = chapters.map(chapter => ({\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    }));\n    existingChapters.push(...newChapters);\n    writeJsonFile(CHAPTERS_FILE, existingChapters);\n    return newChapters;\n  },\n\n  delete: (id: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const index = chapters.findIndex(chapter => chapter.id === id);\n    if (index === -1) return false;\n\n    chapters.splice(index, 1);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);\n    writeJsonFile(CHAPTERS_FILE, filteredChapters);\n    return true;\n  }\n};\n\n// 改写规则相关操作\nexport const ruleDb = {\n  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),\n\n  getById: (id: string): RewriteRule | undefined => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    return rules.find(rule => rule.id === id);\n  },\n\n  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const newRule: RewriteRule = {\n      ...rule,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    rules.push(newRule);\n    writeJsonFile(RULES_FILE, rules);\n    return newRule;\n  },\n\n  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return null;\n\n    rules[index] = {\n      ...rules[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(RULES_FILE, rules);\n    return rules[index];\n  },\n\n  delete: (id: string): boolean => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return false;\n\n    rules.splice(index, 1);\n    writeJsonFile(RULES_FILE, rules);\n    return true;\n  }\n};\n\n// 改写任务相关操作\nexport const jobDb = {\n  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),\n\n  getById: (id: string): RewriteJob | undefined => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    return jobs.find(job => job.id === id);\n  },\n\n  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const newJob: RewriteJob = {\n      ...job,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    jobs.push(newJob);\n    writeJsonFile(JOBS_FILE, jobs);\n    return newJob;\n  },\n\n  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return null;\n\n    jobs[index] = {\n      ...jobs[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(JOBS_FILE, jobs);\n    return jobs[index];\n  },\n\n  delete: (id: string): boolean => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return false;\n\n    jobs.splice(index, 1);\n    writeJsonFile(JOBS_FILE, jobs);\n    return true;\n  }\n};\n\n// 人物设定相关操作\nexport const characterDb = {\n  getAll: (): Character[] => readJsonFile<Character>(CHARACTERS_FILE),\n\n  getByNovelId: (novelId: string): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId);\n  },\n\n  getById: (id: string): Character | undefined => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.find(character => character.id === id);\n  },\n\n  create: (character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Character => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const newCharacter: Character = {\n      ...character,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    characters.push(newCharacter);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return newCharacter;\n  },\n\n  update: (id: string, updates: Partial<Character>): Character | null => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return null;\n\n    characters[index] = {\n      ...characters[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return characters[index];\n  },\n\n  delete: (id: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return false;\n\n    characters.splice(index, 1);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const filteredCharacters = characters.filter(character => character.novelId !== novelId);\n    writeJsonFile(CHARACTERS_FILE, filteredCharacters);\n    return true;\n  }\n};\n\n// 预设相关操作\nexport const presetDb = {\n  getAll: (): Preset[] => readJsonFile<Preset>(PRESETS_FILE),\n\n  getById: (id: string): Preset | undefined => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    return presets.find(preset => preset.id === id);\n  },\n\n  create: (preset: Omit<Preset, 'id' | 'createdAt' | 'updatedAt'>): Preset => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const newPreset: Preset = {\n      ...preset,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    presets.push(newPreset);\n    writeJsonFile(PRESETS_FILE, presets);\n    return newPreset;\n  },\n\n  update: (id: string, updates: Partial<Preset>): Preset | null => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return null;\n\n    presets[index] = {\n      ...presets[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(PRESETS_FILE, presets);\n    return presets[index];\n  },\n\n  delete: (id: string): boolean => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return false;\n\n    presets.splice(index, 1);\n    writeJsonFile(PRESETS_FILE, presets);\n    return true;\n  }\n};\n\n// 小说上下文相关操作\nexport const novelContextDb = {\n  getAll: (): NovelContext[] => readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE),\n\n  getByNovelId: (novelId: string): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    return contexts.find(context => context.novelId === novelId);\n  },\n\n  getById: (id: string): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    return contexts.find(context => context.id === id);\n  },\n\n  create: (context: Omit<NovelContext, 'id' | 'createdAt' | 'updatedAt'>): NovelContext => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const newContext: NovelContext = {\n      ...context,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    contexts.push(newContext);\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return newContext;\n  },\n\n  update: (id: string, updates: Partial<Omit<NovelContext, 'id' | 'createdAt'>>): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return undefined;\n\n    contexts[index] = {\n      ...contexts[index],\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    };\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return contexts[index];\n  },\n\n  delete: (id: string): boolean => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return false;\n\n    contexts.splice(index, 1);\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return true;\n  }\n};\n\n// 章节上下文相关操作\nexport const chapterContextDb = {\n  getAll: (): ChapterContext[] => readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE),\n\n  getByNovelId: (novelId: string): ChapterContext[] => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.filter(context => context.novelId === novelId);\n  },\n\n  getByChapter: (novelId: string, chapterNumber: number): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.find(context =>\n      context.novelId === novelId && context.chapterNumber === chapterNumber\n    );\n  },\n\n  getById: (id: string): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.find(context => context.id === id);\n  },\n\n  create: (context: Omit<ChapterContext, 'id' | 'createdAt' | 'updatedAt'>): ChapterContext => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const newContext: ChapterContext = {\n      ...context,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    contexts.push(newContext);\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return newContext;\n  },\n\n  update: (id: string, updates: Partial<Omit<ChapterContext, 'id' | 'createdAt'>>): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return undefined;\n\n    contexts[index] = {\n      ...contexts[index],\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    };\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return contexts[index];\n  },\n\n  delete: (id: string): boolean => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return false;\n\n    contexts.splice(index, 1);\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return true;\n  },\n\n  // 获取章节的上下文窗口（前后几章的上下文）\n  getContextWindow: (novelId: string, chapterNumber: number, windowSize: number = 2): ChapterContext[] => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const novelContexts = contexts.filter(context => context.novelId === novelId);\n\n    const startChapter = Math.max(1, chapterNumber - windowSize);\n    const endChapter = chapterNumber + windowSize;\n\n    return novelContexts.filter(context =>\n      context.chapterNumber >= startChapter && context.chapterNumber <= endChapter\n    ).sort((a, b) => a.chapterNumber - b.chapterNumber);\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAmIA,SAAS;AACT,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,UAAU;AACxC,MAAM,gBAAgB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAC1C,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,UAAU;AACvC,MAAM,YAAY,4GAAI,CAAC,IAAI,CAAC,UAAU;AACtC,MAAM,kBAAkB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAC5C,MAAM,eAAe,4GAAI,CAAC,IAAI,CAAC,UAAU;AACzC,MAAM,sBAAsB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAChD,MAAM,wBAAwB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAElD,WAAW;AACX,SAAS;IACP,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,wGAAE,CAAC,SAAS,CAAC,UAAU;YAAE,WAAW;QAAK;IAC3C;AACF;AAEA,WAAW;AACX,SAAS,aAAgB,QAAgB;IACvC;IACA,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,OAAO,EAAE;IACX;IACA,IAAI;QACF,MAAM,OAAO,wGAAE,CAAC,YAAY,CAAC,UAAU;QACvC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,OAAO,EAAE;IACX;AACF;AAEA,WAAW;AACX,SAAS,cAAiB,QAAgB,EAAE,IAAS;IACnD;IACA,IAAI;QACF,wGAAE,CAAC,aAAa,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,MAAM;IACR;AACF;AAEA,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;AACxE;AAEA,cAAc;AACd,SAAS,wBAAwB,OAAe;IAC9C,OAAO,gHAAM,CAAC,UAAU,CAAC,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7E;AAGO,MAAM,UAAU;IACrB,QAAQ,IAAe,aAAoB;IAE3C,SAAS,CAAC;QACR,MAAM,SAAS,aAAoB;QACnC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC3C;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QAEnC,cAAc;QACd,MAAM,UAAU,wBAAwB,MAAM,KAAK;QAEnD,iBAAiB;QACjB,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,eAAe;YACjB,eAAe;YACf,cAAc,QAAQ,GAAG,MAAM,QAAQ;YACvC,cAAc,YAAY,GAAG,MAAM,YAAY;YAC/C,cAAc,aAAa;YAC3B,OAAO;QACT;QAEA,MAAM,WAAkB;YACtB,GAAG,KAAK;YACR,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,OAAO,IAAI,CAAC;QACZ,cAAc,aAAa;QAC3B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,CAAC,MAAM,GAAG;YAAE,GAAG,MAAM,CAAC,MAAM;YAAE,GAAG,OAAO;QAAC;QAC/C,cAAc,aAAa;QAC3B,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,MAAM,CAAC,OAAO;QACrB,cAAc,aAAa;QAC3B,OAAO;IACT;AACF;AAGO,MAAM,YAAY;IACvB,QAAQ,IAAiB,aAAsB;IAE/C,cAAc,CAAC;QACb,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACxD;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,aAAsB;YAC1B,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,aAAa,CAAC;QACZ,MAAM,mBAAmB,aAAsB;QAC/C,MAAM,cAAc,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC3C,GAAG,OAAO;gBACV,IAAI;gBACJ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;QACD,iBAAiB,IAAI,IAAI;QACzB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,WAAW,aAAsB;QACvC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QACxE,cAAc,eAAe;QAC7B,OAAO;IACT;AACF;AAGO,MAAM,SAAS;IACpB,QAAQ,IAAqB,aAA0B;IAEvD,SAAS,CAAC;QACR,MAAM,QAAQ,aAA0B;QACxC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxC;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,UAAuB;YAC3B,GAAG,IAAI;YACP,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,MAAM,IAAI,CAAC;QACX,cAAc,YAAY;QAC1B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,CAAC,MAAM,GAAG;YACb,GAAG,KAAK,CAAC,MAAM;YACf,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,YAAY;QAC1B,OAAO,KAAK,CAAC,MAAM;IACrB;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,MAAM,CAAC,OAAO;QACpB,cAAc,YAAY;QAC1B,OAAO;IACT;AACF;AAGO,MAAM,QAAQ;IACnB,QAAQ,IAAoB,aAAyB;IAErD,SAAS,CAAC;QACR,MAAM,OAAO,aAAyB;QACtC,OAAO,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACrC;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,SAAqB;YACzB,GAAG,GAAG;YACN,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,KAAK,IAAI,CAAC;QACV,cAAc,WAAW;QACzB,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,WAAW;QACzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,MAAM,CAAC,OAAO;QACnB,cAAc,WAAW;QACzB,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ,IAAmB,aAAwB;IAEnD,cAAc,CAAC;QACb,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;IAC9D;IAEA,SAAS,CAAC;QACR,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;IACvD;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,eAA0B;YAC9B,GAAG,SAAS;YACZ,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,WAAW,IAAI,CAAC;QAChB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,UAAU,CAAC,MAAM,GAAG;YAClB,GAAG,UAAU,CAAC,MAAM;YACpB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,iBAAiB;QAC/B,OAAO,UAAU,CAAC,MAAM;IAC1B;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,WAAW,MAAM,CAAC,OAAO;QACzB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,aAAa,aAAwB;QAC3C,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;QAChF,cAAc,iBAAiB;QAC/B,OAAO;IACT;AACF;AAGO,MAAM,WAAW;IACtB,QAAQ,IAAgB,aAAqB;IAE7C,SAAS,CAAC;QACR,MAAM,UAAU,aAAqB;QACrC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;IAC9C;IAEA,QAAQ,CAAC;QACP,MAAM,UAAU,aAAqB;QACrC,MAAM,YAAoB;YACxB,GAAG,MAAM;YACT,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,QAAQ,IAAI,CAAC;QACb,cAAc,cAAc;QAC5B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,UAAU,aAAqB;QACrC,MAAM,QAAQ,QAAQ,SAAS,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QACxD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,CAAC,MAAM,GAAG;YACf,GAAG,OAAO,CAAC,MAAM;YACjB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,cAAc;QAC5B,OAAO,OAAO,CAAC,MAAM;IACvB;IAEA,QAAQ,CAAC;QACP,MAAM,UAAU,aAAqB;QACrC,MAAM,QAAQ,QAAQ,SAAS,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QACxD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,QAAQ,MAAM,CAAC,OAAO;QACtB,cAAc,cAAc;QAC5B,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB;IAC5B,QAAQ,IAAsB,aAA2B;IAEzD,cAAc,CAAC;QACb,MAAM,WAAW,aAA2B;QAC5C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACtD;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAA2B;QAC5C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAA2B;QAC5C,MAAM,aAA2B;YAC/B,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,qBAAqB;QACnC,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,WAAW,aAA2B;QAC5C,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,QAAQ,CAAC,MAAM,GAAG;YAChB,GAAG,QAAQ,CAAC,MAAM;YAClB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,qBAAqB;QACnC,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAA2B;QAC5C,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,qBAAqB;QACnC,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB;IAC9B,QAAQ,IAAwB,aAA6B;IAE7D,cAAc,CAAC;QACb,MAAM,WAAW,aAA6B;QAC9C,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACxD;IAEA,cAAc,CAAC,SAAiB;QAC9B,MAAM,WAAW,aAA6B;QAC9C,OAAO,SAAS,IAAI,CAAC,CAAA,UACnB,QAAQ,OAAO,KAAK,WAAW,QAAQ,aAAa,KAAK;IAE7D;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAA6B;QAC9C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAA6B;QAC9C,MAAM,aAA6B;YACjC,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,uBAAuB;QACrC,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,WAAW,aAA6B;QAC9C,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,QAAQ,CAAC,MAAM,GAAG;YAChB,GAAG,QAAQ,CAAC,MAAM;YAClB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,uBAAuB;QACrC,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAA6B;QAC9C,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,uBAAuB;QACrC,OAAO;IACT;IAEA,uBAAuB;IACvB,kBAAkB,CAAC,SAAiB,eAAuB,aAAqB,CAAC;QAC/E,MAAM,WAAW,aAA6B;QAC9C,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QAErE,MAAM,eAAe,KAAK,GAAG,CAAC,GAAG,gBAAgB;QACjD,MAAM,aAAa,gBAAgB;QAEnC,OAAO,cAAc,MAAM,CAAC,CAAA,UAC1B,QAAQ,aAAa,IAAI,gBAAgB,QAAQ,aAAa,IAAI,YAClE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;IACpD;AACF", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/context-client.ts"], "sourcesContent": ["// 客户端上下文工具函数\nimport type { NovelContext, ChapterContext } from './database';\n\n// 检查是否在客户端环境\nfunction isClientSide(): boolean {\n  return typeof window !== 'undefined';\n}\n\n// 客户端获取小说上下文\nexport async function getNovelContextClient(novelId: string): Promise<NovelContext | null> {\n  if (!isClientSide()) {\n    throw new Error('This function can only be used on client side');\n  }\n\n  try {\n    const response = await fetch(`/api/context/novel?novelId=${novelId}`);\n    const result = await response.json();\n    \n    if (!result.success) {\n      console.warn('获取小说上下文失败:', result.error);\n      return null;\n    }\n\n    return result.data;\n  } catch (error) {\n    console.error('获取小说上下文失败:', error);\n    return null;\n  }\n}\n\n// 客户端获取章节上下文\nexport async function getChapterContextClient(novelId: string, chapterNumber: number): Promise<ChapterContext | null> {\n  if (!isClientSide()) {\n    throw new Error('This function can only be used on client side');\n  }\n\n  try {\n    const response = await fetch(`/api/context/chapter?novelId=${novelId}&chapterNumber=${chapterNumber}`);\n    const result = await response.json();\n    \n    if (!result.success) {\n      console.warn('获取章节上下文失败:', result.error);\n      return null;\n    }\n\n    return result.data;\n  } catch (error) {\n    console.error('获取章节上下文失败:', error);\n    return null;\n  }\n}\n\n// 客户端获取章节上下文窗口\nexport async function getChapterContextWindowClient(\n  novelId: string,\n  chapterNumber: number,\n  windowSize: number = 2\n): Promise<ChapterContext[]> {\n  if (!isClientSide()) {\n    throw new Error('This function can only be used on client side');\n  }\n\n  try {\n    const response = await fetch(`/api/context/window?novelId=${novelId}&chapterNumber=${chapterNumber}&windowSize=${windowSize}`);\n    const result = await response.json();\n    \n    if (!result.success) {\n      console.warn('获取章节上下文窗口失败:', result.error);\n      return [];\n    }\n\n    return result.data.contexts;\n  } catch (error) {\n    console.error('获取章节上下文窗口失败:', error);\n    return [];\n  }\n}\n\n// 客户端分析小说上下文\nexport async function analyzeNovelClient(novelId: string, analyzeChapters: boolean = false): Promise<{\n  novelContext: NovelContext;\n  chapterContexts?: ChapterContext[];\n}> {\n  if (!isClientSide()) {\n    throw new Error('This function can only be used on client side');\n  }\n\n  const response = await fetch('/api/context/analyze', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({ \n      novelId: novelId,\n      analyzeChapters: analyzeChapters \n    })\n  });\n\n  const result = await response.json();\n  \n  if (!result.success) {\n    throw new Error(result.error || '分析失败');\n  }\n\n  return {\n    novelContext: result.data.novelContext,\n    chapterContexts: result.data.chapterContexts\n  };\n}\n\n// 客户端带上下文的重写函数\nexport async function rewriteTextWithContextClient(\n  novelId: string,\n  chapterNumbers: number[],\n  rules: string,\n  model?: string,\n  onProgress?: (progress: number, currentChapter: number) => void\n): Promise<Array<{ success: boolean; content: string; error?: string }>> {\n  if (!isClientSide()) {\n    throw new Error('This function can only be used on client side');\n  }\n\n  const response = await fetch('/api/rewrite', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({\n      novelId: novelId,\n      chapterNumbers: chapterNumbers,\n      rules: rules,\n      model: model || 'gemini-2.5-flash-lite'\n    })\n  });\n\n  const result = await response.json();\n  \n  if (!result.success) {\n    throw new Error(result.error || '重写失败');\n  }\n\n  return result.data.results;\n}\n\n// 通用的上下文获取函数（自动选择服务端或客户端）\nexport async function getNovelContext(novelId: string): Promise<NovelContext | null> {\n  if (isClientSide()) {\n    return getNovelContextClient(novelId);\n  } else {\n    // 服务端环境，动态导入服务端函数\n    const { getNovelContextServer } = await import('./context-utils');\n    return getNovelContextServer(novelId);\n  }\n}\n\n// 通用的章节上下文获取函数（自动选择服务端或客户端）\nexport async function getChapterContext(novelId: string, chapterNumber: number): Promise<ChapterContext | null> {\n  if (isClientSide()) {\n    return getChapterContextClient(novelId, chapterNumber);\n  } else {\n    // 服务端环境，动态导入服务端函数\n    const { getChapterContextServer } = await import('./context-utils');\n    return getChapterContextServer(novelId, chapterNumber);\n  }\n}\n\n// 通用的章节上下文窗口获取函数（自动选择服务端或客户端）\nexport async function getChapterContextWindow(\n  novelId: string,\n  chapterNumber: number,\n  windowSize: number = 2\n): Promise<ChapterContext[]> {\n  if (isClientSide()) {\n    return getChapterContextWindowClient(novelId, chapterNumber, windowSize);\n  } else {\n    // 服务端环境，动态导入服务端函数\n    const { getChapterContextWindowServer } = await import('./context-utils');\n    return getChapterContextWindowServer(novelId, chapterNumber, windowSize);\n  }\n}\n"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;;;;;;;;;;;AAGb,aAAa;AACb,SAAS;IACP,OAAO,gBAAkB;AAC3B;AAGO,eAAe,sBAAsB,OAAe;IACzD,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,2BAA2B,EAAE,SAAS;QACpE,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,IAAI,CAAC,cAAc,OAAO,KAAK;YACvC,OAAO;QACT;QAEA,OAAO,OAAO,IAAI;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO;IACT;AACF;AAGO,eAAe,wBAAwB,OAAe,EAAE,aAAqB;IAClF,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,6BAA6B,EAAE,QAAQ,eAAe,EAAE,eAAe;QACrG,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,IAAI,CAAC,cAAc,OAAO,KAAK;YACvC,OAAO;QACT;QAEA,OAAO,OAAO,IAAI;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO;IACT;AACF;AAGO,eAAe,8BACpB,OAAe,EACf,aAAqB,EACrB,aAAqB,CAAC;IAEtB,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,4BAA4B,EAAE,QAAQ,eAAe,EAAE,cAAc,YAAY,EAAE,YAAY;QAC7H,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,IAAI,CAAC,gBAAgB,OAAO,KAAK;YACzC,OAAO,EAAE;QACX;QAEA,OAAO,OAAO,IAAI,CAAC,QAAQ;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,EAAE;IACX;AACF;AAGO,eAAe,mBAAmB,OAAe,EAAE,kBAA2B,KAAK;IAIxF,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,wBAAwB;QACnD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;YACnB,SAAS;YACT,iBAAiB;QACnB;IACF;IAEA,MAAM,SAAS,MAAM,SAAS,IAAI;IAElC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO;QACL,cAAc,OAAO,IAAI,CAAC,YAAY;QACtC,iBAAiB,OAAO,IAAI,CAAC,eAAe;IAC9C;AACF;AAGO,eAAe,6BACpB,OAAe,EACf,cAAwB,EACxB,KAAa,EACb,KAAc,EACd,UAA+D;IAE/D,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,gBAAgB;QAC3C,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;YACnB,SAAS;YACT,gBAAgB;YAChB,OAAO;YACP,OAAO,SAAS;QAClB;IACF;IAEA,MAAM,SAAS,MAAM,SAAS,IAAI;IAElC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI,CAAC,OAAO;AAC5B;AAGO,eAAe,gBAAgB,OAAe;IACnD,IAAI;;SAEG;QACL,kBAAkB;QAClB,MAAM,EAAE,qBAAqB,EAAE,GAAG;QAClC,OAAO,sBAAsB;IAC/B;AACF;AAGO,eAAe,kBAAkB,OAAe,EAAE,aAAqB;IAC5E,IAAI;;SAEG;QACL,kBAAkB;QAClB,MAAM,EAAE,uBAAuB,EAAE,GAAG;QACpC,OAAO,wBAAwB,SAAS;IAC1C;AACF;AAGO,eAAe,wBACpB,OAAe,EACf,aAAqB,EACrB,aAAqB,CAAC;IAEtB,IAAI;;SAEG;QACL,kBAAkB;QAClB,MAAM,EAAE,6BAA6B,EAAE,GAAG;QAC1C,OAAO,8BAA8B,SAAS,eAAe;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/context-utils.ts"], "sourcesContent": ["// 上下文工具函数 - 服务端和客户端通用\nimport type { RewriteRequest, RewriteResponse } from './gemini';\n\n// 检查是否在服务端环境\nfunction isServerSide(): boolean {\n  return typeof window === 'undefined';\n}\n\n// 带上下文的重写函数（服务端专用）\nexport async function rewriteTextWithContextServer(\n  novelId: string,\n  chapterNumber: number,\n  originalText: string,\n  rules: string,\n  chapterTitle?: string,\n  model?: string\n): Promise<RewriteResponse> {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  try {\n    // 导入服务端模块\n    const { rewriteText } = await import('./gemini');\n    const { novelContextDb, chapterContextDb } = await import('./database');\n\n    // 获取小说整体上下文\n    const novelContext = novelContextDb.getByNovelId(novelId);\n\n    // 获取章节上下文\n    const chapterContext = chapterContextDb.getByChapter(novelId, chapterNumber);\n\n    // 构建请求\n    const request: RewriteRequest = {\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model,\n      novelContext: novelContext ? {\n        summary: novelContext.summary,\n        mainCharacters: novelContext.mainCharacters,\n        worldSetting: novelContext.worldSetting,\n        writingStyle: novelContext.writingStyle,\n        tone: novelContext.tone\n      } : undefined,\n      chapterContext: chapterContext ? {\n        previousChapterSummary: chapterContext.previousChapterSummary,\n        keyEvents: chapterContext.keyEvents,\n        characterStates: chapterContext.characterStates,\n        plotProgress: chapterContext.plotProgress,\n        contextualNotes: chapterContext.contextualNotes\n      } : undefined\n    };\n\n    return await rewriteText(request);\n  } catch (error) {\n    console.error('带上下文重写失败:', error);\n    // 如果获取上下文失败，回退到普通重写\n    const { rewriteText } = await import('./gemini');\n    return await rewriteText({\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model\n    });\n  }\n}\n\n// 获取小说上下文（服务端专用）\nexport async function getNovelContextServer(novelId: string) {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  const { novelContextDb } = await import('./database');\n  return novelContextDb.getByNovelId(novelId);\n}\n\n// 获取章节上下文（服务端专用）\nexport async function getChapterContextServer(novelId: string, chapterNumber: number) {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  const { chapterContextDb } = await import('./database');\n  return chapterContextDb.getByChapter(novelId, chapterNumber);\n}\n\n// 获取章节上下文窗口（服务端专用）\nexport async function getChapterContextWindowServer(\n  novelId: string,\n  chapterNumber: number,\n  windowSize: number = 2\n) {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  const { chapterContextDb } = await import('./database');\n  return chapterContextDb.getContextWindow(novelId, chapterNumber, windowSize);\n}\n\n// 带上下文的批量重写函数（服务端专用）\nexport async function rewriteChaptersWithContext(\n  novelId: string,\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number, details?: any) => void,\n  onChapterComplete?: (chapterIndex: number, result: any) => void,\n  concurrency: number = 3,\n  model: string = 'gemini-2.5-flash-lite',\n  enableFailureRecovery: boolean = true\n): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  try {\n    // 导入所需模块\n    const { novelContextDb, chapterContextDb } = await import('./database');\n    const { rewriteText } = await import('./gemini');\n\n    // 获取小说整体上下文\n    const novelContext = novelContextDb.getByNovelId(novelId);\n\n    const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);\n    let completed = 0;\n    let totalTokensUsed = 0;\n    const startTime = Date.now();\n\n    // 使用信号量控制并发\n    const semaphore = { count: concurrency, waiting: [] as Array<() => void> };\n\n    const acquire = () => new Promise<void>(resolve => {\n      if (semaphore.count > 0) {\n        semaphore.count--;\n        resolve();\n      } else {\n        semaphore.waiting.push(resolve);\n      }\n    });\n\n    const release = () => {\n      semaphore.count++;\n      if (semaphore.waiting.length > 0) {\n        const next = semaphore.waiting.shift();\n        if (next) {\n          semaphore.count--;\n          next();\n        }\n      }\n    };\n\n    const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {\n      await acquire();\n      const chapterStartTime = Date.now();\n\n      try {\n        // 获取章节上下文\n        const chapterContext = chapterContextDb.getByChapter(novelId, chapter.number);\n\n        // 构建带上下文的请求\n        const request: RewriteRequest = {\n          originalText: chapter.content,\n          rules,\n          chapterTitle: chapter.title,\n          chapterNumber: chapter.number,\n          model,\n          novelContext: novelContext ? {\n            summary: novelContext.summary,\n            mainCharacters: novelContext.mainCharacters,\n            worldSetting: novelContext.worldSetting,\n            writingStyle: novelContext.writingStyle,\n            tone: novelContext.tone\n          } : undefined,\n          chapterContext: chapterContext ? {\n            previousChapterSummary: chapterContext.previousChapterSummary,\n            keyEvents: chapterContext.keyEvents,\n            characterStates: chapterContext.characterStates,\n            plotProgress: chapterContext.plotProgress,\n            contextualNotes: chapterContext.contextualNotes\n          } : undefined\n        };\n\n        const result = await rewriteText(request);\n        const chapterProcessingTime = Date.now() - chapterStartTime;\n\n        if (result.tokensUsed) {\n          totalTokensUsed += result.tokensUsed;\n        }\n\n        const chapterResult = {\n          success: result.success,\n          content: result.rewrittenText,\n          error: result.error,\n          details: {\n            apiKeyUsed: result.apiKeyUsed,\n            tokensUsed: result.tokensUsed,\n            model: result.model,\n            processingTime: chapterProcessingTime,\n            chapterNumber: chapter.number,\n            chapterTitle: chapter.title,\n            hasContext: !!(novelContext || chapterContext)\n          }\n        };\n\n        results[index] = chapterResult;\n        completed++;\n\n        // 实时回调章节完成\n        if (onChapterComplete) {\n          onChapterComplete(index, chapterResult);\n        }\n\n        // 更新进度\n        const progress = (completed / chapters.length) * 100;\n        const totalTime = Date.now() - startTime;\n        const averageTimePerChapter = totalTime / completed;\n\n        if (onProgress) {\n          onProgress(progress, chapter.number, {\n            completed,\n            totalTokensUsed,\n            totalTime,\n            averageTimePerChapter,\n            hasContext: !!(novelContext || chapterContext)\n          });\n        }\n\n        console.log(`第 ${chapter.number} 章重写${result.success ? '成功' : '失败'}${novelContext || chapterContext ? '（使用上下文）' : ''}: ${result.error || '完成'}`);\n\n      } catch (error) {\n        const chapterProcessingTime = Date.now() - chapterStartTime;\n        const chapterResult = {\n          success: false,\n          content: '',\n          error: `重写异常: ${error instanceof Error ? error.message : '未知错误'}`,\n          details: {\n            processingTime: chapterProcessingTime,\n            chapterNumber: chapter.number,\n            chapterTitle: chapter.title,\n            hasContext: !!(novelContext || chapterContext)\n          }\n        };\n\n        results[index] = chapterResult;\n        completed++;\n\n        if (onChapterComplete) {\n          onChapterComplete(index, chapterResult);\n        }\n\n        console.error(`第 ${chapter.number} 章重写异常:`, error);\n      } finally {\n        release();\n      }\n    };\n\n    // 并行处理所有章节\n    const promises = chapters.map((chapter, index) => processChapter(chapter, index));\n    await Promise.all(promises);\n\n    return results;\n\n  } catch (error) {\n    console.error('批量重写失败，回退到普通重写:', error);\n    // 如果上下文重写失败，回退到普通重写\n    const { rewriteChapters } = await import('./gemini');\n    return await rewriteChapters(\n      chapters,\n      rules,\n      onProgress,\n      onChapterComplete,\n      concurrency,\n      model,\n      enableFailureRecovery\n    );\n  }\n}\n\n// 重新导出客户端函数，提供统一接口\nexport {\n  getNovelContext,\n  getChapterContext,\n  getChapterContextWindow,\n  getNovelContextClient,\n  getChapterContextClient,\n  getChapterContextWindowClient,\n  analyzeNovelClient,\n  rewriteTextWithContextClient\n} from './context-client';\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;AA0RtB,mBAAmB;AACnB;AAxRA,aAAa;AACb,SAAS;IACP,OAAO,gBAAkB;AAC3B;AAGO,eAAe,6BACpB,OAAe,EACf,aAAqB,EACrB,YAAoB,EACpB,KAAa,EACb,YAAqB,EACrB,KAAc;IAEd,IAAI,CAAC;;IAIL,IAAI;QACF,UAAU;QACV,MAAM,EAAE,WAAW,EAAE,GAAG;QACxB,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG;QAE7C,YAAY;QACZ,MAAM,eAAe,eAAe,YAAY,CAAC;QAEjD,UAAU;QACV,MAAM,kBAAiB,iBAAiB,YAAY,CAAC,SAAS;QAE9D,OAAO;QACP,MAAM,UAA0B;YAC9B;YACA;YACA;YACA;YACA;YACA,cAAc,eAAe;gBAC3B,SAAS,aAAa,OAAO;gBAC7B,gBAAgB,aAAa,cAAc;gBAC3C,cAAc,aAAa,YAAY;gBACvC,cAAc,aAAa,YAAY;gBACvC,MAAM,aAAa,IAAI;YACzB,IAAI;YACJ,gBAAgB,kBAAiB;gBAC/B,wBAAwB,gBAAe,sBAAsB;gBAC7D,WAAW,gBAAe,SAAS;gBACnC,iBAAiB,gBAAe,eAAe;gBAC/C,cAAc,gBAAe,YAAY;gBACzC,iBAAiB,gBAAe,eAAe;YACjD,IAAI;QACN;QAEA,OAAO,MAAM,YAAY;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,oBAAoB;QACpB,MAAM,EAAE,WAAW,EAAE,GAAG;QACxB,OAAO,MAAM,YAAY;YACvB;YACA;YACA;YACA;YACA;QACF;IACF;AACF;AAGO,eAAe,sBAAsB,OAAe;IACzD,IAAI,CAAC;;IAIL,MAAM,EAAE,cAAc,EAAE,GAAG;IAC3B,OAAO,eAAe,YAAY,CAAC;AACrC;AAGO,eAAe,wBAAwB,OAAe,EAAE,aAAqB;IAClF,IAAI,CAAC;;IAIL,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,OAAO,iBAAiB,YAAY,CAAC,SAAS;AAChD;AAGO,eAAe,8BACpB,OAAe,EACf,aAAqB,EACrB,aAAqB,CAAC;IAEtB,IAAI,CAAC;;IAIL,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,OAAO,iBAAiB,gBAAgB,CAAC,SAAS,eAAe;AACnE;AAGO,eAAe,2BACpB,OAAe,EACf,QAAmE,EACnE,KAAa,EACb,UAA8E,EAC9E,iBAA+D,EAC/D,cAAsB,CAAC,EACvB,QAAgB,uBAAuB,EACvC,wBAAiC,IAAI;IAErC,IAAI,CAAC;;IAIL,IAAI;QACF,SAAS;QACT,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG;QAC7C,MAAM,EAAE,WAAW,EAAE,GAAG;QAExB,YAAY;QACZ,MAAM,eAAe,eAAe,YAAY,CAAC;QAEjD,MAAM,UAAuF,IAAI,MAAM,SAAS,MAAM;QACtH,IAAI,YAAY;QAChB,IAAI,kBAAkB;QACtB,MAAM,YAAY,KAAK,GAAG;QAE1B,YAAY;QACZ,MAAM,YAAY;YAAE,OAAO;YAAa,SAAS,EAAE;QAAsB;QAEzE,MAAM,UAAU,IAAM,IAAI,QAAc,CAAA;gBACtC,IAAI,UAAU,KAAK,GAAG,GAAG;oBACvB,UAAU,KAAK;oBACf;gBACF,OAAO;oBACL,UAAU,OAAO,CAAC,IAAI,CAAC;gBACzB;YACF;QAEA,MAAM,UAAU;YACd,UAAU,KAAK;YACf,IAAI,UAAU,OAAO,CAAC,MAAM,GAAG,GAAG;gBAChC,MAAM,OAAO,UAAU,OAAO,CAAC,KAAK;gBACpC,IAAI,MAAM;oBACR,UAAU,KAAK;oBACf;gBACF;YACF;QACF;QAEA,MAAM,iBAAiB,OAAO,SAA6D;YACzF,MAAM;YACN,MAAM,mBAAmB,KAAK,GAAG;YAEjC,IAAI;gBACF,UAAU;gBACV,MAAM,kBAAiB,iBAAiB,YAAY,CAAC,SAAS,QAAQ,MAAM;gBAE5E,YAAY;gBACZ,MAAM,UAA0B;oBAC9B,cAAc,QAAQ,OAAO;oBAC7B;oBACA,cAAc,QAAQ,KAAK;oBAC3B,eAAe,QAAQ,MAAM;oBAC7B;oBACA,cAAc,eAAe;wBAC3B,SAAS,aAAa,OAAO;wBAC7B,gBAAgB,aAAa,cAAc;wBAC3C,cAAc,aAAa,YAAY;wBACvC,cAAc,aAAa,YAAY;wBACvC,MAAM,aAAa,IAAI;oBACzB,IAAI;oBACJ,gBAAgB,kBAAiB;wBAC/B,wBAAwB,gBAAe,sBAAsB;wBAC7D,WAAW,gBAAe,SAAS;wBACnC,iBAAiB,gBAAe,eAAe;wBAC/C,cAAc,gBAAe,YAAY;wBACzC,iBAAiB,gBAAe,eAAe;oBACjD,IAAI;gBACN;gBAEA,MAAM,SAAS,MAAM,YAAY;gBACjC,MAAM,wBAAwB,KAAK,GAAG,KAAK;gBAE3C,IAAI,OAAO,UAAU,EAAE;oBACrB,mBAAmB,OAAO,UAAU;gBACtC;gBAEA,MAAM,gBAAgB;oBACpB,SAAS,OAAO,OAAO;oBACvB,SAAS,OAAO,aAAa;oBAC7B,OAAO,OAAO,KAAK;oBACnB,SAAS;wBACP,YAAY,OAAO,UAAU;wBAC7B,YAAY,OAAO,UAAU;wBAC7B,OAAO,OAAO,KAAK;wBACnB,gBAAgB;wBAChB,eAAe,QAAQ,MAAM;wBAC7B,cAAc,QAAQ,KAAK;wBAC3B,YAAY,CAAC,CAAC,CAAC,gBAAgB,eAAc;oBAC/C;gBACF;gBAEA,OAAO,CAAC,MAAM,GAAG;gBACjB;gBAEA,WAAW;gBACX,IAAI,mBAAmB;oBACrB,kBAAkB,OAAO;gBAC3B;gBAEA,OAAO;gBACP,MAAM,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI;gBACjD,MAAM,YAAY,KAAK,GAAG,KAAK;gBAC/B,MAAM,wBAAwB,YAAY;gBAE1C,IAAI,YAAY;oBACd,WAAW,UAAU,QAAQ,MAAM,EAAE;wBACnC;wBACA;wBACA;wBACA;wBACA,YAAY,CAAC,CAAC,CAAC,gBAAgB,eAAc;oBAC/C;gBACF;gBAEA,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,OAAO,gBAAgB,kBAAiB,YAAY,GAAG,EAAE,EAAE,OAAO,KAAK,IAAI,MAAM;YAEjJ,EAAE,OAAO,OAAO;gBACd,MAAM,wBAAwB,KAAK,GAAG,KAAK;gBAC3C,MAAM,gBAAgB;oBACpB,SAAS;oBACT,SAAS;oBACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;oBACjE,SAAS;wBACP,gBAAgB;wBAChB,eAAe,QAAQ,MAAM;wBAC7B,cAAc,QAAQ,KAAK;wBAC3B,YAAY,CAAC,CAAC,CAAC,gBAAgB,cAAc;oBAC/C;gBACF;gBAEA,OAAO,CAAC,MAAM,GAAG;gBACjB;gBAEA,IAAI,mBAAmB;oBACrB,kBAAkB,OAAO;gBAC3B;gBAEA,QAAQ,KAAK,CAAC,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC,EAAE;YAC9C,SAAU;gBACR;YACF;QACF;QAEA,WAAW;QACX,MAAM,WAAW,SAAS,GAAG,CAAC,CAAC,SAAS,QAAU,eAAe,SAAS;QAC1E,MAAM,QAAQ,GAAG,CAAC;QAElB,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,oBAAoB;QACpB,MAAM,EAAE,eAAe,EAAE,GAAG;QAC5B,OAAO,MAAM,gBACX,UACA,OACA,YACA,mBACA,aACA,OACA;IAEJ;AACF", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/file-manager.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\n// 文件管理工具类\nexport class FileManager {\n  private static instance: FileManager;\n  private baseDir: string;\n\n  private constructor() {\n    this.baseDir = process.cwd();\n  }\n\n  public static getInstance(): FileManager {\n    if (!FileManager.instance) {\n      FileManager.instance = new FileManager();\n    }\n    return FileManager.instance;\n  }\n\n  // 确保目录存在\n  public ensureDir(dirPath: string): void {\n    if (!fs.existsSync(dirPath)) {\n      fs.mkdirSync(dirPath, { recursive: true });\n    }\n  }\n\n  // 获取novels目录路径\n  public getNovelsDir(): string {\n    return path.join(this.baseDir, '..', 'novels');\n  }\n\n  // 获取chapters目录路径\n  public getChaptersDir(): string {\n    return path.join(this.baseDir, '..', 'chapters');\n  }\n\n  // 获取数据目录路径\n  public getDataDir(): string {\n    const dataDir = path.join(this.baseDir, 'data');\n    this.ensureDir(dataDir);\n    return dataDir;\n  }\n\n  // 获取改写结果目录路径\n  public getRewrittenDir(): string {\n    const rewrittenDir = path.join(this.getDataDir(), 'rewritten');\n    this.ensureDir(rewrittenDir);\n    return rewrittenDir;\n  }\n\n  // 获取特定小说的改写结果目录\n  public getNovelRewrittenDir(novelTitle: string): string {\n    const novelDir = path.join(this.getRewrittenDir(), this.sanitizeFilename(novelTitle));\n    this.ensureDir(novelDir);\n    return novelDir;\n  }\n\n  // 获取完成小说目录路径\n  public getDoneNovelsDir(): string {\n    const doneNovelsDir = path.join(this.baseDir, '..', 'done-novels');\n    this.ensureDir(doneNovelsDir);\n    return doneNovelsDir;\n  }\n\n  // 获取特定小说的章节目录\n  public getNovelChaptersDir(novelTitle: string): string {\n    const chaptersDir = this.getChaptersDir();\n    this.ensureDir(chaptersDir);\n    const novelDir = path.join(chaptersDir, this.sanitizeFilename(novelTitle));\n    this.ensureDir(novelDir);\n    return novelDir;\n  }\n\n  // 清理文件名中的非法字符\n  public sanitizeFilename(filename: string): string {\n    return filename.replace(/[<>:\"/\\\\|?*]/g, '_').trim();\n  }\n\n  // 读取文件内容\n  public readFile(filePath: string): string {\n    try {\n      return fs.readFileSync(filePath, 'utf-8');\n    } catch (error) {\n      console.error(`读取文件失败: ${filePath}`, error);\n      throw error;\n    }\n  }\n\n  // 写入文件内容\n  public writeFile(filePath: string, content: string): void {\n    try {\n      const dir = path.dirname(filePath);\n      this.ensureDir(dir);\n      fs.writeFileSync(filePath, content, 'utf-8');\n    } catch (error) {\n      console.error(`写入文件失败: ${filePath}`, error);\n      throw error;\n    }\n  }\n\n  // 检查文件是否存在\n  public fileExists(filePath: string): boolean {\n    return fs.existsSync(filePath);\n  }\n\n  // 获取目录中的所有文件\n  public listFiles(dirPath: string, extensions?: string[]): string[] {\n    try {\n      if (!fs.existsSync(dirPath)) {\n        return [];\n      }\n\n      const files = fs.readdirSync(dirPath);\n\n      if (extensions) {\n        return files.filter(file => {\n          const ext = path.extname(file).toLowerCase();\n          return extensions.includes(ext);\n        });\n      }\n\n      return files;\n    } catch (error) {\n      console.error(`读取目录失败: ${dirPath}`, error);\n      return [];\n    }\n  }\n\n  // 获取文件信息\n  public getFileStats(filePath: string): fs.Stats | null {\n    try {\n      return fs.statSync(filePath);\n    } catch (error) {\n      console.error(`获取文件信息失败: ${filePath}`, error);\n      return null;\n    }\n  }\n\n  // 删除文件\n  public deleteFile(filePath: string): boolean {\n    try {\n      if (fs.existsSync(filePath)) {\n        fs.unlinkSync(filePath);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(`删除文件失败: ${filePath}`, error);\n      return false;\n    }\n  }\n\n  // 删除目录\n  public deleteDir(dirPath: string): boolean {\n    try {\n      if (fs.existsSync(dirPath)) {\n        fs.rmSync(dirPath, { recursive: true, force: true });\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(`删除目录失败: ${dirPath}`, error);\n      return false;\n    }\n  }\n\n  // 复制文件\n  public copyFile(srcPath: string, destPath: string): boolean {\n    try {\n      const destDir = path.dirname(destPath);\n      this.ensureDir(destDir);\n      fs.copyFileSync(srcPath, destPath);\n      return true;\n    } catch (error) {\n      console.error(`复制文件失败: ${srcPath} -> ${destPath}`, error);\n      return false;\n    }\n  }\n\n  // 移动文件\n  public moveFile(srcPath: string, destPath: string): boolean {\n    try {\n      const destDir = path.dirname(destPath);\n      this.ensureDir(destDir);\n      fs.renameSync(srcPath, destPath);\n      return true;\n    } catch (error) {\n      console.error(`移动文件失败: ${srcPath} -> ${destPath}`, error);\n      return false;\n    }\n  }\n\n  // 获取目录大小\n  public getDirSize(dirPath: string): number {\n    let totalSize = 0;\n\n    try {\n      if (!fs.existsSync(dirPath)) {\n        return 0;\n      }\n\n      const files = fs.readdirSync(dirPath);\n\n      for (const file of files) {\n        const filePath = path.join(dirPath, file);\n        const stats = fs.statSync(filePath);\n\n        if (stats.isDirectory()) {\n          totalSize += this.getDirSize(filePath);\n        } else {\n          totalSize += stats.size;\n        }\n      }\n    } catch (error) {\n      console.error(`计算目录大小失败: ${dirPath}`, error);\n    }\n\n    return totalSize;\n  }\n\n  // 格式化文件大小\n  public formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 B';\n\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  // 创建备份\n  public createBackup(filePath: string): string | null {\n    try {\n      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n      const ext = path.extname(filePath);\n      const baseName = path.basename(filePath, ext);\n      const dir = path.dirname(filePath);\n\n      const backupPath = path.join(dir, `${baseName}_backup_${timestamp}${ext}`);\n\n      if (this.copyFile(filePath, backupPath)) {\n        return backupPath;\n      }\n\n      return null;\n    } catch (error) {\n      console.error(`创建备份失败: ${filePath}`, error);\n      return null;\n    }\n  }\n\n  // 合并改写的章节为一个完整的小说文件\n  public mergeRewrittenChapters(novelTitle: string): { success: boolean; filePath?: string; error?: string } {\n    try {\n      const rewrittenDir = this.getNovelRewrittenDir(novelTitle);\n      const doneNovelsDir = this.getDoneNovelsDir();\n\n      // 获取所有改写的章节文件\n      const chapterFiles = this.listFiles(rewrittenDir, ['.txt'])\n        .filter(file => file.startsWith('chapter_') && file.includes('_rewritten'))\n        .sort((a, b) => {\n          // 提取章节号进行排序\n          const aNum = parseInt(a.match(/chapter_(\\d+)/)?.[1] || '0');\n          const bNum = parseInt(b.match(/chapter_(\\d+)/)?.[1] || '0');\n          return aNum - bNum;\n        });\n\n      if (chapterFiles.length === 0) {\n        return { success: false, error: '没有找到改写的章节文件' };\n      }\n\n      // 读取并合并所有章节\n      let mergedContent = '';\n      for (const chapterFile of chapterFiles) {\n        const chapterPath = path.join(rewrittenDir, chapterFile);\n        const chapterContent = this.readFile(chapterPath);\n        mergedContent += chapterContent + '\\n\\n';\n      }\n\n      // 生成合并后的文件名\n      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);\n      const mergedFileName = `${this.sanitizeFilename(novelTitle)}_merged_${timestamp}.txt`;\n      const mergedFilePath = path.join(doneNovelsDir, mergedFileName);\n\n      // 写入合并后的文件\n      this.writeFile(mergedFilePath, mergedContent.trim());\n\n      return {\n        success: true,\n        filePath: mergedFilePath\n      };\n    } catch (error) {\n      console.error(`合并章节失败: ${novelTitle}`, error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '未知错误'\n      };\n    }\n  }\n\n  // 清理旧备份文件\n  public cleanupBackups(dirPath: string, maxBackups: number = 5): void {\n    try {\n      const files = this.listFiles(dirPath);\n      const backupFiles = files\n        .filter(file => file.includes('_backup_'))\n        .map(file => ({\n          name: file,\n          path: path.join(dirPath, file),\n          stats: this.getFileStats(path.join(dirPath, file))\n        }))\n        .filter(item => item.stats !== null)\n        .sort((a, b) => b.stats!.mtime.getTime() - a.stats!.mtime.getTime());\n\n      // 删除超出数量限制的备份文件\n      if (backupFiles.length > maxBackups) {\n        const filesToDelete = backupFiles.slice(maxBackups);\n        for (const file of filesToDelete) {\n          this.deleteFile(file.path);\n        }\n      }\n    } catch (error) {\n      console.error(`清理备份文件失败: ${dirPath}`, error);\n    }\n  }\n}\n\n// 导出单例实例\nexport const fileManager = FileManager.getInstance();\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAGO,MAAM;IACX,OAAe,SAAsB;IAC7B,QAAgB;IAExB,aAAsB;QACpB,IAAI,CAAC,OAAO,GAAG,QAAQ,GAAG;IAC5B;IAEA,OAAc,cAA2B;QACvC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA,SAAS;IACF,UAAU,OAAe,EAAQ;QACtC,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;YAC3B,wGAAE,CAAC,SAAS,CAAC,SAAS;gBAAE,WAAW;YAAK;QAC1C;IACF;IAEA,eAAe;IACR,eAAuB;QAC5B,OAAO,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;IACvC;IAEA,iBAAiB;IACV,iBAAyB;QAC9B,OAAO,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;IACvC;IAEA,WAAW;IACJ,aAAqB;QAC1B,MAAM,UAAU,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACxC,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,aAAa;IACN,kBAA0B;QAC/B,MAAM,eAAe,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI;QAClD,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,gBAAgB;IACT,qBAAqB,UAAkB,EAAU;QACtD,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,CAAC;QACzE,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,aAAa;IACN,mBAA2B;QAChC,MAAM,gBAAgB,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;QACpD,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,cAAc;IACP,oBAAoB,UAAkB,EAAU;QACrD,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,IAAI,CAAC,SAAS,CAAC;QACf,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,gBAAgB,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,cAAc;IACP,iBAAiB,QAAgB,EAAU;QAChD,OAAO,SAAS,OAAO,CAAC,iBAAiB,KAAK,IAAI;IACpD;IAEA,SAAS;IACF,SAAS,QAAgB,EAAU;QACxC,IAAI;YACF,OAAO,wGAAE,CAAC,YAAY,CAAC,UAAU;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,MAAM;QACR;IACF;IAEA,SAAS;IACF,UAAU,QAAgB,EAAE,OAAe,EAAQ;QACxD,IAAI;YACF,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,aAAa,CAAC,UAAU,SAAS;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,MAAM;QACR;IACF;IAEA,WAAW;IACJ,WAAW,QAAgB,EAAW;QAC3C,OAAO,wGAAE,CAAC,UAAU,CAAC;IACvB;IAEA,aAAa;IACN,UAAU,OAAe,EAAE,UAAqB,EAAY;QACjE,IAAI;YACF,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,OAAO,EAAE;YACX;YAEA,MAAM,QAAQ,wGAAE,CAAC,WAAW,CAAC;YAE7B,IAAI,YAAY;gBACd,OAAO,MAAM,MAAM,CAAC,CAAA;oBAClB,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC,MAAM,WAAW;oBAC1C,OAAO,WAAW,QAAQ,CAAC;gBAC7B;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE;YACpC,OAAO,EAAE;QACX;IACF;IAEA,SAAS;IACF,aAAa,QAAgB,EAAmB;QACrD,IAAI;YACF,OAAO,wGAAE,CAAC,QAAQ,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE;YACvC,OAAO;QACT;IACF;IAEA,OAAO;IACA,WAAW,QAAgB,EAAW;QAC3C,IAAI;YACF,IAAI,wGAAE,CAAC,UAAU,CAAC,WAAW;gBAC3B,wGAAE,CAAC,UAAU,CAAC;gBACd,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;IACA,UAAU,OAAe,EAAW;QACzC,IAAI;YACF,IAAI,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC1B,wGAAE,CAAC,MAAM,CAAC,SAAS;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAClD,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE;YACpC,OAAO;QACT;IACF;IAEA,OAAO;IACA,SAAS,OAAe,EAAE,QAAgB,EAAW;QAC1D,IAAI;YACF,MAAM,UAAU,4GAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,YAAY,CAAC,SAAS;YACzB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE,UAAU,EAAE;YACnD,OAAO;QACT;IACF;IAEA,OAAO;IACA,SAAS,OAAe,EAAE,QAAgB,EAAW;QAC1D,IAAI;YACF,MAAM,UAAU,4GAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,UAAU,CAAC,SAAS;YACvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE,UAAU,EAAE;YACnD,OAAO;QACT;IACF;IAEA,SAAS;IACF,WAAW,OAAe,EAAU;QACzC,IAAI,YAAY;QAEhB,IAAI;YACF,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,OAAO;YACT;YAEA,MAAM,QAAQ,wGAAE,CAAC,WAAW,CAAC;YAE7B,KAAK,MAAM,QAAQ,MAAO;gBACxB,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,SAAS;gBACpC,MAAM,QAAQ,wGAAE,CAAC,QAAQ,CAAC;gBAE1B,IAAI,MAAM,WAAW,IAAI;oBACvB,aAAa,IAAI,CAAC,UAAU,CAAC;gBAC/B,OAAO;oBACL,aAAa,MAAM,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE;QACxC;QAEA,OAAO;IACT;IAEA,UAAU;IACH,eAAe,KAAa,EAAU;QAC3C,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,OAAO;IACA,aAAa,QAAgB,EAAiB;QACnD,IAAI;YACF,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,SAAS;YAC5D,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YACzB,MAAM,WAAW,4GAAI,CAAC,QAAQ,CAAC,UAAU;YACzC,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YAEzB,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,QAAQ,EAAE,YAAY,KAAK;YAEzE,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,aAAa;gBACvC,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,OAAO;QACT;IACF;IAEA,oBAAoB;IACb,uBAAuB,UAAkB,EAA2D;QACzG,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,oBAAoB,CAAC;YAC/C,MAAM,gBAAgB,IAAI,CAAC,gBAAgB;YAE3C,cAAc;YACd,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,cAAc;gBAAC;aAAO,EACvD,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC,eAAe,KAAK,QAAQ,CAAC,eAC5D,IAAI,CAAC,CAAC,GAAG;gBACR,YAAY;gBACZ,MAAM,OAAO,SAAS,EAAE,KAAK,CAAC,kBAAkB,CAAC,EAAE,IAAI;gBACvD,MAAM,OAAO,SAAS,EAAE,KAAK,CAAC,kBAAkB,CAAC,EAAE,IAAI;gBACvD,OAAO,OAAO;YAChB;YAEF,IAAI,aAAa,MAAM,KAAK,GAAG;gBAC7B,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAc;YAChD;YAEA,YAAY;YACZ,IAAI,gBAAgB;YACpB,KAAK,MAAM,eAAe,aAAc;gBACtC,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,cAAc;gBAC5C,MAAM,iBAAiB,IAAI,CAAC,QAAQ,CAAC;gBACrC,iBAAiB,iBAAiB;YACpC;YAEA,YAAY;YACZ,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC,GAAG;YAC9E,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,QAAQ,EAAE,UAAU,IAAI,CAAC;YACrF,MAAM,iBAAiB,4GAAI,CAAC,IAAI,CAAC,eAAe;YAEhD,WAAW;YACX,IAAI,CAAC,SAAS,CAAC,gBAAgB,cAAc,IAAI;YAEjD,OAAO;gBACL,SAAS;gBACT,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,YAAY,EAAE;YACvC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACH,eAAe,OAAe,EAAE,aAAqB,CAAC,EAAQ;QACnE,IAAI;YACF,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC;YAC7B,MAAM,cAAc,MACjB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,aAC7B,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACZ,MAAM;oBACN,MAAM,4GAAI,CAAC,IAAI,CAAC,SAAS;oBACzB,OAAO,IAAI,CAAC,YAAY,CAAC,4GAAI,CAAC,IAAI,CAAC,SAAS;gBAC9C,CAAC,GACA,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,MAC9B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,CAAE,KAAK,CAAC,OAAO,KAAK,EAAE,KAAK,CAAE,KAAK,CAAC,OAAO;YAEnE,gBAAgB;YAChB,IAAI,YAAY,MAAM,GAAG,YAAY;gBACnC,MAAM,gBAAgB,YAAY,KAAK,CAAC;gBACxC,KAAK,MAAM,QAAQ,cAAe;oBAChC,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE;QACxC;IACF;AACF;AAGO,MAAM,cAAc,YAAY,WAAW", "debugId": null}}, {"offset": {"line": 1188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/novel-parser.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { novelDb, chapterDb, Novel, Chapter } from './database';\nimport { fileManager } from './file-manager';\n\n// 小说解析配置\ninterface ParseConfig {\n  chapterPattern: RegExp;\n  minChapterLength: number;\n  maxChapterLength: number;\n}\n\n// 分卷/分集匹配模式（用于分栏，不作为章节）\nconst VOLUME_PATTERNS = [\n  /^\\s*(?:第[一二三四五六七八九十百千万\\d]+[卷集部])\\s*.*$/gmi,\n  /^\\s*(?:[卷集部][一二三四五六七八九十百千万\\d]+)\\s*.*$/gmi,\n];\n\n// 章节匹配模式\nconst CHAPTER_PATTERNS = [\n  /^\\s*(?:第[一二三四五六七八九十百千万\\d]+[章节回])\\s*.*$/gmi,\n  /^\\s*(?:Chapter\\s+\\d+)\\s*.*$/gmi,\n];\n\n// 解析小说文件\nexport async function parseNovelFile(filePath: string): Promise<{\n  novel: Novel;\n  chapters: Chapter[];\n}> {\n  const filename = path.basename(filePath);\n  const title = filename.replace(/\\.(txt|md)$/i, '');\n\n  // 读取文件内容\n  const content = fs.readFileSync(filePath, 'utf-8');\n\n  // 创建小说记录\n  const novel = novelDb.create({\n    title,\n    filename,\n  });\n\n  // 解析章节\n  const chapters = parseChapters(content, novel.id);\n\n  // 批量创建章节记录\n  const createdChapters = chapterDb.createBatch(chapters);\n\n  // 更新小说的章节数量\n  novelDb.update(novel.id, { chapterCount: createdChapters.length });\n\n  // 保存章节文件\n  await saveChapterFiles(createdChapters);\n\n  return {\n    novel: { ...novel, chapterCount: createdChapters.length },\n    chapters: createdChapters,\n  };\n}\n\n// 解析章节内容\nfunction parseChapters(content: string, novelId: string): Omit<Chapter, 'id' | 'createdAt'>[] {\n  const chapters: Omit<Chapter, 'id' | 'createdAt'>[] = [];\n\n  // 首先识别分卷/分集标记\n  const volumeMatches = findVolumeMarkers(content);\n\n  // 然后在每个分卷内或整个文本中查找章节\n  if (volumeMatches.length > 0) {\n    // 有分卷的情况\n    console.log(`Found ${volumeMatches.length} volumes`);\n    let chapterNumber = 1;\n\n    for (let i = 0; i < volumeMatches.length; i++) {\n      const volumeStart = volumeMatches[i].index;\n      const volumeEnd = i + 1 < volumeMatches.length ? volumeMatches[i + 1].index : content.length;\n      const volumeContent = content.slice(volumeStart, volumeEnd);\n\n      // 在分卷内查找章节\n      const volumeChapters = parseChaptersInVolume(volumeContent, novelId, chapterNumber, volumeMatches[i].title);\n      chapters.push(...volumeChapters);\n      chapterNumber += volumeChapters.length;\n    }\n  } else {\n    // 没有分卷，直接解析章节\n    const directChapters = parseChaptersInVolume(content, novelId, 1);\n    chapters.push(...directChapters);\n  }\n\n  console.log(`Successfully parsed ${chapters.length} chapters`);\n  return chapters;\n}\n\n// 查找分卷标记\nfunction findVolumeMarkers(content: string): Array<{ index: number; title: string }> {\n  const volumeMarkers: Array<{ index: number; title: string }> = [];\n\n  for (const pattern of VOLUME_PATTERNS) {\n    const matches = Array.from(content.matchAll(pattern));\n    for (const match of matches) {\n      volumeMarkers.push({\n        index: match.index!,\n        title: extractChapterTitle(match[0])\n      });\n    }\n  }\n\n  // 按位置排序\n  return volumeMarkers.sort((a, b) => a.index - b.index);\n}\n\n// 在指定内容中解析章节（重构版）\nfunction parseChaptersInVolume(\n  content: string,\n  novelId: string,\n  startChapterNumber: number,\n  volumeTitle?: string\n): Omit<Chapter, 'id' | 'createdAt'>[] {\n  const chapters: Omit<Chapter, 'id' | 'createdAt'>[] = [];\n  let chapterNumber = startChapterNumber;\n\n  // 1. 寻找最佳匹配模式\n  let bestPattern: RegExp | null = null;\n  let bestMatchesCount = -1;\n\n  for (const pattern of CHAPTER_PATTERNS) {\n    const matches = content.match(pattern); // 使用 match 而不是 matchAll 来计数\n    const matchCount = matches ? matches.length : 0;\n    if (matchCount > bestMatchesCount) {\n      bestMatchesCount = matchCount;\n      bestPattern = pattern;\n    }\n  }\n\n  // 2. 如果没有找到任何章节标记，将整个内容作为一章\n  if (!bestPattern || bestMatchesCount === 0) {\n    const trimmedContent = content.trim();\n    if (trimmedContent.length > 100) { // 只有内容足够长才作为章节\n      chapters.push({\n        novelId,\n        chapterNumber: chapterNumber,\n        title: volumeTitle || '全文',\n        content: trimmedContent,\n        filename: `chapter_${chapterNumber}.txt`,\n      });\n    }\n    return chapters;\n  }\n\n  // 3. 使用 split 进行分割 (关键改动)\n  // 创建一个带捕获组的新正则表达式，以便 split 保留分隔符\n  const splitPattern = new RegExp(`(${bestPattern.source})`, 'gmi');\n  const parts = content.split(splitPattern);\n\n  // parts 数组的结构会是: [前言部分, 标题1, 内容1, 标题2, 内容2, ...]\n\n  let currentContent = '';\n\n  // 处理可能存在的前言/序章（parts[0]）\n  const prologue = parts[0]?.trim();\n  if (prologue && prologue.length > 100) {\n    chapters.push({\n      novelId,\n      chapterNumber: chapterNumber,\n      // 你可以给它一个固定的名字，或者尝试从内容中提取\n      title: '序章',\n      content: prologue,\n      filename: `chapter_${chapterNumber}.txt`,\n    });\n    chapterNumber++;\n  }\n  console.log(parts.map((v, i) => i + v));\n\n  // 4. 循环处理分割后的部分\n  for (let i = 1; i < parts.length; i += 2) {\n\n    const titlePart = parts[i];\n    const contentPart = parts[i + 1] || ''; // 后面的内容部分\n\n    if (!titlePart) continue;\n\n    const trimmedContent = (titlePart + contentPart).trim();\n\n    if (trimmedContent.length > 100) { // 检查章节总长度\n      chapters.push({\n        novelId,\n        chapterNumber: chapterNumber,\n        title: extractChapterTitle(titlePart), // 标题就是分割符本身\n        content: trimmedContent,\n        filename: `chapter_${chapterNumber}.txt`,\n      });\n      chapterNumber++;\n    }\n  }\n\n  return chapters;\n}\n// 提取章节标题\nfunction extractChapterTitle(chapterText: string): string {\n  const lines = chapterText.trim().split('\\n');\n  const firstLine = lines[0].trim();\n\n  // 如果第一行看起来像标题，使用它\n  if (firstLine.length < 100 && firstLine.length > 0) {\n    return firstLine;\n  }\n\n  // 否则尝试从前几行中找到标题\n  for (let i = 0; i < Math.min(3, lines.length); i++) {\n    const line = lines[i].trim();\n    if (line.length > 0 && line.length < 100) {\n      return line;\n    }\n  }\n\n  return '未命名章节';\n}\n\n// 保存章节文件到chapters目录\nasync function saveChapterFiles(chapters: Chapter[]): Promise<void> {\n  // 为每个小说创建子目录\n  const novelIds = [...new Set(chapters.map(ch => ch.novelId))];\n\n  for (const novelId of novelIds) {\n    const novel = novelDb.getById(novelId);\n    if (!novel) continue;\n\n    const novelDir = fileManager.getNovelChaptersDir(novel.title);\n\n    // 保存该小说的所有章节\n    const novelChapters = chapters.filter(ch => ch.novelId === novelId);\n    for (const chapter of novelChapters) {\n      const chapterPath = path.join(novelDir, chapter.filename);\n      fileManager.writeFile(chapterPath, chapter.content);\n    }\n  }\n}\n\n// 获取novels目录中的所有小说文件\nexport function getAvailableNovels(): string[] {\n  const novelsDir = fileManager.getNovelsDir();\n  return fileManager.listFiles(novelsDir, ['.txt', '.md']);\n}\n\n// 检查小说是否已经被解析\nexport function isNovelParsed(filename: string): boolean {\n  const novels = novelDb.getAll();\n  return novels.some(novel => novel.filename === filename);\n}\n\n// 重新解析小说（删除旧数据并重新解析）\nexport async function reparseNovel(filename: string): Promise<{\n  novel: Novel;\n  chapters: Chapter[];\n} | null> {\n  const novelsDir = fileManager.getNovelsDir();\n  const filePath = path.join(novelsDir, filename);\n\n  if (!fileManager.fileExists(filePath)) {\n    return null;\n  }\n\n  // 删除旧的小说和章节数据\n  const existingNovels = novelDb.getAll();\n  const existingNovel = existingNovels.find(novel => novel.filename === filename);\n\n  if (existingNovel) {\n    chapterDb.deleteByNovelId(existingNovel.id);\n    novelDb.delete(existingNovel.id);\n  }\n\n  // 重新解析\n  return await parseNovelFile(filePath);\n}\n\n// 解析章节范围字符串 (例如: \"1-5,7,10-12\")\nexport function parseChapterRange(rangeStr: string, maxChapter: number): number[] {\n  const chapters: number[] = [];\n  const parts = rangeStr.split(',').map(part => part.trim());\n\n  for (const part of parts) {\n    if (part.includes('-')) {\n      // 范围格式 (例如: \"1-5\")\n      const [start, end] = part.split('-').map(num => parseInt(num.trim()));\n      if (!isNaN(start) && !isNaN(end) && start <= end) {\n        for (let i = start; i <= Math.min(end, maxChapter); i++) {\n          if (i > 0 && !chapters.includes(i)) {\n            chapters.push(i);\n          }\n        }\n      }\n    } else {\n      // 单个章节\n      const chapterNum = parseInt(part);\n      if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {\n        chapters.push(chapterNum);\n      }\n    }\n  }\n\n  return chapters.sort((a, b) => a - b);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AASA,wBAAwB;AACxB,MAAM,kBAAkB;IACtB;IACA;CACD;AAED,SAAS;AACT,MAAM,mBAAmB;IACvB;IACA;CACD;AAGM,eAAe,eAAe,QAAgB;IAInD,MAAM,WAAW,4GAAI,CAAC,QAAQ,CAAC;IAC/B,MAAM,QAAQ,SAAS,OAAO,CAAC,gBAAgB;IAE/C,SAAS;IACT,MAAM,UAAU,wGAAE,CAAC,YAAY,CAAC,UAAU;IAE1C,SAAS;IACT,MAAM,QAAQ,mIAAO,CAAC,MAAM,CAAC;QAC3B;QACA;IACF;IAEA,OAAO;IACP,MAAM,WAAW,cAAc,SAAS,MAAM,EAAE;IAEhD,WAAW;IACX,MAAM,kBAAkB,qIAAS,CAAC,WAAW,CAAC;IAE9C,YAAY;IACZ,mIAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE;QAAE,cAAc,gBAAgB,MAAM;IAAC;IAEhE,SAAS;IACT,MAAM,iBAAiB;IAEvB,OAAO;QACL,OAAO;YAAE,GAAG,KAAK;YAAE,cAAc,gBAAgB,MAAM;QAAC;QACxD,UAAU;IACZ;AACF;AAEA,SAAS;AACT,SAAS,cAAc,OAAe,EAAE,OAAe;IACrD,MAAM,WAAgD,EAAE;IAExD,cAAc;IACd,MAAM,gBAAgB,kBAAkB;IAExC,qBAAqB;IACrB,IAAI,cAAc,MAAM,GAAG,GAAG;QAC5B,SAAS;QACT,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,cAAc,MAAM,CAAC,QAAQ,CAAC;QACnD,IAAI,gBAAgB;QAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;YAC7C,MAAM,cAAc,aAAa,CAAC,EAAE,CAAC,KAAK;YAC1C,MAAM,YAAY,IAAI,IAAI,cAAc,MAAM,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,QAAQ,MAAM;YAC5F,MAAM,gBAAgB,QAAQ,KAAK,CAAC,aAAa;YAEjD,WAAW;YACX,MAAM,iBAAiB,sBAAsB,eAAe,SAAS,eAAe,aAAa,CAAC,EAAE,CAAC,KAAK;YAC1G,SAAS,IAAI,IAAI;YACjB,iBAAiB,eAAe,MAAM;QACxC;IACF,OAAO;QACL,cAAc;QACd,MAAM,iBAAiB,sBAAsB,SAAS,SAAS;QAC/D,SAAS,IAAI,IAAI;IACnB;IAEA,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;IAC7D,OAAO;AACT;AAEA,SAAS;AACT,SAAS,kBAAkB,OAAe;IACxC,MAAM,gBAAyD,EAAE;IAEjE,KAAK,MAAM,WAAW,gBAAiB;QACrC,MAAM,UAAU,MAAM,IAAI,CAAC,QAAQ,QAAQ,CAAC;QAC5C,KAAK,MAAM,SAAS,QAAS;YAC3B,cAAc,IAAI,CAAC;gBACjB,OAAO,MAAM,KAAK;gBAClB,OAAO,oBAAoB,KAAK,CAAC,EAAE;YACrC;QACF;IACF;IAEA,QAAQ;IACR,OAAO,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACvD;AAEA,kBAAkB;AAClB,SAAS,sBACP,OAAe,EACf,OAAe,EACf,kBAA0B,EAC1B,WAAoB;IAEpB,MAAM,WAAgD,EAAE;IACxD,IAAI,gBAAgB;IAEpB,cAAc;IACd,IAAI,cAA6B;IACjC,IAAI,mBAAmB,CAAC;IAExB,KAAK,MAAM,WAAW,iBAAkB;QACtC,MAAM,UAAU,QAAQ,KAAK,CAAC,UAAU,4BAA4B;QACpE,MAAM,aAAa,UAAU,QAAQ,MAAM,GAAG;QAC9C,IAAI,aAAa,kBAAkB;YACjC,mBAAmB;YACnB,cAAc;QAChB;IACF;IAEA,4BAA4B;IAC5B,IAAI,CAAC,eAAe,qBAAqB,GAAG;QAC1C,MAAM,iBAAiB,QAAQ,IAAI;QACnC,IAAI,eAAe,MAAM,GAAG,KAAK;YAC/B,SAAS,IAAI,CAAC;gBACZ;gBACA,eAAe;gBACf,OAAO,eAAe;gBACtB,SAAS;gBACT,UAAU,CAAC,QAAQ,EAAE,cAAc,IAAI,CAAC;YAC1C;QACF;QACA,OAAO;IACT;IAEA,0BAA0B;IAC1B,iCAAiC;IACjC,MAAM,eAAe,IAAI,OAAO,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,CAAC,CAAC,EAAE;IAC3D,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAE5B,iDAAiD;IAEjD,IAAI,iBAAiB;IAErB,yBAAyB;IACzB,MAAM,WAAW,KAAK,CAAC,EAAE,EAAE;IAC3B,IAAI,YAAY,SAAS,MAAM,GAAG,KAAK;QACrC,SAAS,IAAI,CAAC;YACZ;YACA,eAAe;YACf,0BAA0B;YAC1B,OAAO;YACP,SAAS;YACT,UAAU,CAAC,QAAQ,EAAE,cAAc,IAAI,CAAC;QAC1C;QACA;IACF;IACA,QAAQ,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAM,IAAI;IAEpC,gBAAgB;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;QAExC,MAAM,YAAY,KAAK,CAAC,EAAE;QAC1B,MAAM,cAAc,KAAK,CAAC,IAAI,EAAE,IAAI,IAAI,UAAU;QAElD,IAAI,CAAC,WAAW;QAEhB,MAAM,iBAAiB,CAAC,YAAY,WAAW,EAAE,IAAI;QAErD,IAAI,eAAe,MAAM,GAAG,KAAK;YAC/B,SAAS,IAAI,CAAC;gBACZ;gBACA,eAAe;gBACf,OAAO,oBAAoB;gBAC3B,SAAS;gBACT,UAAU,CAAC,QAAQ,EAAE,cAAc,IAAI,CAAC;YAC1C;YACA;QACF;IACF;IAEA,OAAO;AACT;AACA,SAAS;AACT,SAAS,oBAAoB,WAAmB;IAC9C,MAAM,QAAQ,YAAY,IAAI,GAAG,KAAK,CAAC;IACvC,MAAM,YAAY,KAAK,CAAC,EAAE,CAAC,IAAI;IAE/B,kBAAkB;IAClB,IAAI,UAAU,MAAM,GAAG,OAAO,UAAU,MAAM,GAAG,GAAG;QAClD,OAAO;IACT;IAEA,gBAAgB;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,IAAK;QAClD,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;QAC1B,IAAI,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,GAAG,KAAK;YACxC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,oBAAoB;AACpB,eAAe,iBAAiB,QAAmB;IACjD,aAAa;IACb,MAAM,WAAW;WAAI,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,KAAM,GAAG,OAAO;KAAG;IAE7D,KAAK,MAAM,WAAW,SAAU;QAC9B,MAAM,QAAQ,mIAAO,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,WAAW,8IAAW,CAAC,mBAAmB,CAAC,MAAM,KAAK;QAE5D,aAAa;QACb,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK;QAC3D,KAAK,MAAM,WAAW,cAAe;YACnC,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,UAAU,QAAQ,QAAQ;YACxD,8IAAW,CAAC,SAAS,CAAC,aAAa,QAAQ,OAAO;QACpD;IACF;AACF;AAGO,SAAS;IACd,MAAM,YAAY,8IAAW,CAAC,YAAY;IAC1C,OAAO,8IAAW,CAAC,SAAS,CAAC,WAAW;QAAC;QAAQ;KAAM;AACzD;AAGO,SAAS,cAAc,QAAgB;IAC5C,MAAM,SAAS,mIAAO,CAAC,MAAM;IAC7B,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AACjD;AAGO,eAAe,aAAa,QAAgB;IAIjD,MAAM,YAAY,8IAAW,CAAC,YAAY;IAC1C,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,WAAW;IAEtC,IAAI,CAAC,8IAAW,CAAC,UAAU,CAAC,WAAW;QACrC,OAAO;IACT;IAEA,cAAc;IACd,MAAM,iBAAiB,mIAAO,CAAC,MAAM;IACrC,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;IAEtE,IAAI,eAAe;QACjB,qIAAS,CAAC,eAAe,CAAC,cAAc,EAAE;QAC1C,mIAAO,CAAC,MAAM,CAAC,cAAc,EAAE;IACjC;IAEA,OAAO;IACP,OAAO,MAAM,eAAe;AAC9B;AAGO,SAAS,kBAAkB,QAAgB,EAAE,UAAkB;IACpE,MAAM,WAAqB,EAAE;IAC7B,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;IAEvD,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,QAAQ,CAAC,MAAM;YACtB,mBAAmB;YACnB,MAAM,CAAC,OAAO,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,SAAS,IAAI,IAAI;YACjE,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,SAAS,KAAK;gBAChD,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,GAAG,CAAC,KAAK,aAAa,IAAK;oBACvD,IAAI,IAAI,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAI;wBAClC,SAAS,IAAI,CAAC;oBAChB;gBACF;YACF;QACF,OAAO;YACL,OAAO;YACP,MAAM,aAAa,SAAS;YAC5B,IAAI,CAAC,MAAM,eAAe,aAAa,KAAK,cAAc,cAAc,CAAC,SAAS,QAAQ,CAAC,aAAa;gBACtG,SAAS,IAAI,CAAC;YAChB;QACF;IACF;IAEA,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;AACrC", "debugId": null}}, {"offset": {"line": 1446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/app/api/rewrite/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { chapterDb, jobDb, novelDb } from '@/lib/database';\nimport { rewriteChaptersWithContext } from '@/lib/context-utils';\nimport { parseChapterRange } from '@/lib/novel-parser';\nimport { fileManager } from '@/lib/file-manager';\nimport path from 'path';\n\n// POST - 创建改写任务\nexport async function POST(request: NextRequest) {\n  try {\n    const { novelId, chapterRange, rules, model, concurrency } = await request.json();\n\n    if (!novelId || !chapterRange || !rules) {\n      return NextResponse.json(\n        { success: false, error: '参数不完整' },\n        { status: 400 }\n      );\n    }\n\n    // 验证模型和并发数参数\n    const validModels = ['gemini-2.5-flash-lite', 'gemini-2.5-flash', 'gemini-1.5-pro'];\n    const selectedModel = model && validModels.includes(model) ? model : 'gemini-2.5-flash-lite';\n    const selectedConcurrency = concurrency && concurrency >= 1 && concurrency <= 10 ? concurrency : 3;\n\n    // 获取小说信息\n    const novel = novelDb.getById(novelId);\n    if (!novel) {\n      return NextResponse.json(\n        { success: false, error: '小说不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 获取所有章节\n    const allChapters = chapterDb.getByNovelId(novelId);\n    if (allChapters.length === 0) {\n      return NextResponse.json(\n        { success: false, error: '该小说没有章节' },\n        { status: 404 }\n      );\n    }\n\n    // 解析章节范围\n    const chapterNumbers = parseChapterRange(chapterRange, allChapters.length);\n    if (chapterNumbers.length === 0) {\n      return NextResponse.json(\n        { success: false, error: '无效的章节范围' },\n        { status: 400 }\n      );\n    }\n\n    // 获取要改写的章节\n    const chaptersToRewrite = allChapters.filter(chapter =>\n      chapterNumbers.includes(chapter.chapterNumber)\n    );\n\n    if (chaptersToRewrite.length === 0) {\n      return NextResponse.json(\n        { success: false, error: '没有找到指定的章节' },\n        { status: 404 }\n      );\n    }\n\n    // 创建改写任务\n    const job = jobDb.create({\n      novelId,\n      chapters: chapterNumbers,\n      ruleId: 'custom', // 暂时使用custom，后续可以关联到规则表\n      status: 'pending',\n      progress: 0,\n      details: {\n        totalChapters: chaptersToRewrite.length,\n        completedChapters: 0,\n        failedChapters: 0,\n        totalTokensUsed: 0,\n        totalProcessingTime: 0,\n        averageTimePerChapter: 0,\n        apiKeyStats: [],\n        chapterResults: [],\n        model: selectedModel,\n        concurrency: selectedConcurrency,\n      }\n    });\n\n    // 异步执行改写任务\n    executeRewriteJob(job.id, chaptersToRewrite, rules, novel.title, selectedModel, selectedConcurrency);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        jobId: job.id,\n        chaptersCount: chaptersToRewrite.length,\n        message: '改写任务已创建，正在处理中...',\n      },\n    });\n\n  } catch (error) {\n    console.error('创建改写任务失败:', error);\n    return NextResponse.json(\n      { success: false, error: '创建改写任务失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// 异步执行改写任务 - 支持实时写入和详细进度\nasync function executeRewriteJob(\n  jobId: string,\n  chapters: Array<{\n    id: string;\n    novelId: string;\n    chapterNumber: number;\n    title: string;\n    content: string;\n    filename: string;\n    createdAt: string;\n  }>,\n  rules: string,\n  novelTitle: string,\n  model: string = 'gemini-2.5-flash-lite',\n  concurrency: number = 3\n) {\n  const startTime = Date.now();\n\n  try {\n    // 更新任务状态为处理中\n    jobDb.update(jobId, {\n      status: 'processing',\n      progress: 0,\n      details: {\n        totalChapters: chapters.length,\n        completedChapters: 0,\n        failedChapters: 0,\n        totalTokensUsed: 0,\n        totalProcessingTime: 0,\n        averageTimePerChapter: 0,\n        apiKeyStats: jobDb.getById(jobId)?.details?.apiKeyStats || [],\n        chapterResults: [],\n        model: model,\n        concurrency: concurrency,\n      }\n    });\n\n    // 准备章节数据\n    const chaptersData = chapters.map(chapter => ({\n      content: chapter.content,\n      title: chapter.title,\n      number: chapter.chapterNumber,\n    }));\n\n    // 创建输出目录\n    const outputDir = fileManager.getNovelRewrittenDir(novelTitle);\n\n    // 执行改写（降低并发数以避免429错误，启用失败恢复）\n    const results = await rewriteChaptersWithContext(\n      chapters[0].novelId,\n      chaptersData,\n      rules,\n      // 进度回调 - 更新详细信息\n      (progress: number, _currentChapter: number, details: any) => {\n        const currentJob = jobDb.getById(jobId);\n        if (currentJob && details) {\n          jobDb.update(jobId, {\n            progress: Math.round(progress),\n            details: {\n              totalChapters: currentJob.details?.totalChapters || chapters.length,\n              completedChapters: details.completed,\n              failedChapters: currentJob.details?.failedChapters || 0,\n              totalTokensUsed: details.totalTokensUsed,\n              totalProcessingTime: details.totalTime,\n              averageTimePerChapter: details.averageTimePerChapter,\n              apiKeyStats: details.apiKeyStats,\n              chapterResults: currentJob.details?.chapterResults || [],\n              model: model,\n              concurrency: concurrency,\n            }\n          });\n        }\n      },\n      // 章节完成回调 - 实时写入\n      (chapterIndex: number, result: any) => {\n        if (result.success) {\n          // 立即写入完成的章节\n          const chapter = chapters[chapterIndex];\n          const filename = `chapter_${chapter.chapterNumber}_rewritten.txt`;\n          const filePath = path.join(outputDir, filename);\n          fileManager.writeFile(filePath, result.content);\n        }\n\n        // 更新章节结果到数据库\n        const currentJob = jobDb.getById(jobId);\n        if (currentJob?.details && result) {\n          const chapterResults = [...(currentJob.details.chapterResults || [])];\n          chapterResults[chapterIndex] = {\n            chapterNumber: result.details?.chapterNumber || chapterIndex + 1,\n            chapterTitle: result.details?.chapterTitle || `第${chapterIndex + 1}章`,\n            success: result.success,\n            error: result.error,\n            apiKeyUsed: result.details?.apiKeyUsed,\n            tokensUsed: result.details?.tokensUsed,\n            processingTime: result.details?.processingTime,\n            completedAt: new Date().toISOString(),\n          };\n\n          jobDb.update(jobId, {\n            details: {\n              ...currentJob.details,\n              chapterResults,\n              failedChapters: chapterResults.filter(r => r && !r.success).length,\n            }\n          });\n        }\n      },\n      concurrency, // 使用用户选择的并发数量\n      model, // 使用用户选择的模型\n      true // 启用失败恢复机制\n    );\n\n    // 统计结果\n    let successCount = 0;\n    let totalTokensUsed = 0;\n    const resultSummary: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n    }> = [];\n\n    for (let i = 0; i < results.length; i++) {\n      const result = results[i];\n      const chapter = chapters[i];\n\n      // 检查 result 是否为 null 或 undefined\n      if (result && result.success) {\n        successCount++;\n      }\n\n      if (result?.details?.tokensUsed) {\n        totalTokensUsed += result.details.tokensUsed;\n      }\n\n      resultSummary.push({\n        chapterNumber: chapter.chapterNumber,\n        chapterTitle: chapter.title,\n        success: result ? result.success : false,\n        error: result?.error || '处理失败',\n        apiKeyUsed: result?.details?.apiKeyUsed,\n        tokensUsed: result?.details?.tokensUsed,\n        processingTime: result?.details?.processingTime,\n      });\n    }\n\n    // 保存详细的结果摘要\n    const summaryPath = path.join(outputDir, 'rewrite_summary.json');\n    const totalProcessingTime = Date.now() - startTime;\n\n    const summaryData = JSON.stringify({\n      jobId,\n      novelTitle,\n      rules,\n      totalChapters: chapters.length,\n      successCount,\n      failedCount: chapters.length - successCount,\n      totalTokensUsed,\n      totalProcessingTime,\n      averageTimePerChapter: totalProcessingTime / chapters.length,\n      results: resultSummary,\n      completedAt: new Date().toISOString(),\n      model: model,\n      concurrency: concurrency,\n    }, null, 2);\n    fileManager.writeFile(summaryPath, summaryData);\n\n    // 更新任务状态为完成\n    const currentJob = jobDb.getById(jobId);\n    const finalStatus = successCount > 0 ? 'completed' : 'failed';\n\n    jobDb.update(jobId, {\n      status: finalStatus,\n      progress: 100,\n      result: `成功改写 ${successCount}/${chapters.length} 章节，结果保存在: ${outputDir}`,\n      details: {\n        totalChapters: chapters.length,\n        completedChapters: successCount,\n        failedChapters: chapters.length - successCount,\n        totalTokensUsed,\n        totalProcessingTime,\n        averageTimePerChapter: chapters.length > 0 ? totalProcessingTime / chapters.length : 0,\n        apiKeyStats: currentJob?.details?.apiKeyStats || [],\n        chapterResults: resultSummary,\n        model: model,\n        concurrency: concurrency,\n      }\n    });\n\n  } catch (error) {\n    console.error('执行改写任务失败:', error);\n    jobDb.update(jobId, {\n      status: 'failed',\n      result: `改写失败: ${error instanceof Error ? error.message : '未知错误'}`,\n    });\n  }\n}\n\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE/E,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO;YACvC,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,aAAa;QACb,MAAM,cAAc;YAAC;YAAyB;YAAoB;SAAiB;QACnF,MAAM,gBAAgB,SAAS,YAAY,QAAQ,CAAC,SAAS,QAAQ;QACrE,MAAM,sBAAsB,eAAe,eAAe,KAAK,eAAe,KAAK,cAAc;QAEjG,SAAS;QACT,MAAM,QAAQ,mIAAO,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,OAAO;YACV,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,cAAc,qIAAS,CAAC,YAAY,CAAC;QAC3C,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAU,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,iBAAiB,IAAA,oJAAiB,EAAC,cAAc,YAAY,MAAM;QACzE,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAU,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,oBAAoB,YAAY,MAAM,CAAC,CAAA,UAC3C,eAAe,QAAQ,CAAC,QAAQ,aAAa;QAG/C,IAAI,kBAAkB,MAAM,KAAK,GAAG;YAClC,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAY,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,MAAM,iIAAK,CAAC,MAAM,CAAC;YACvB;YACA,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,SAAS;gBACP,eAAe,kBAAkB,MAAM;gBACvC,mBAAmB;gBACnB,gBAAgB;gBAChB,iBAAiB;gBACjB,qBAAqB;gBACrB,uBAAuB;gBACvB,aAAa,EAAE;gBACf,gBAAgB,EAAE;gBAClB,OAAO;gBACP,aAAa;YACf;QACF;QAEA,WAAW;QACX,kBAAkB,IAAI,EAAE,EAAE,mBAAmB,OAAO,MAAM,KAAK,EAAE,eAAe;QAEhF,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,OAAO,IAAI,EAAE;gBACb,eAAe,kBAAkB,MAAM;gBACvC,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,yBAAyB;AACzB,eAAe,kBACb,KAAa,EACb,QAQE,EACF,KAAa,EACb,UAAkB,EAClB,QAAgB,uBAAuB,EACvC,cAAsB,CAAC;IAEvB,MAAM,YAAY,KAAK,GAAG;IAE1B,IAAI;QACF,aAAa;QACb,iIAAK,CAAC,MAAM,CAAC,OAAO;YAClB,QAAQ;YACR,UAAU;YACV,SAAS;gBACP,eAAe,SAAS,MAAM;gBAC9B,mBAAmB;gBACnB,gBAAgB;gBAChB,iBAAiB;gBACjB,qBAAqB;gBACrB,uBAAuB;gBACvB,aAAa,iIAAK,CAAC,OAAO,CAAC,QAAQ,SAAS,eAAe,EAAE;gBAC7D,gBAAgB,EAAE;gBAClB,OAAO;gBACP,aAAa;YACf;QACF;QAEA,SAAS;QACT,MAAM,eAAe,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC5C,SAAS,QAAQ,OAAO;gBACxB,OAAO,QAAQ,KAAK;gBACpB,QAAQ,QAAQ,aAAa;YAC/B,CAAC;QAED,SAAS;QACT,MAAM,YAAY,8IAAW,CAAC,oBAAoB,CAAC;QAEnD,6BAA6B;QAC7B,MAAM,UAAU,MAAM,IAAA,8KAA0B,EAC9C,QAAQ,CAAC,EAAE,CAAC,OAAO,EACnB,cACA,OACA,gBAAgB;QAChB,CAAC,UAAkB,iBAAyB;YAC1C,MAAM,aAAa,iIAAK,CAAC,OAAO,CAAC;YACjC,IAAI,cAAc,SAAS;gBACzB,iIAAK,CAAC,MAAM,CAAC,OAAO;oBAClB,UAAU,KAAK,KAAK,CAAC;oBACrB,SAAS;wBACP,eAAe,WAAW,OAAO,EAAE,iBAAiB,SAAS,MAAM;wBACnE,mBAAmB,QAAQ,SAAS;wBACpC,gBAAgB,WAAW,OAAO,EAAE,kBAAkB;wBACtD,iBAAiB,QAAQ,eAAe;wBACxC,qBAAqB,QAAQ,SAAS;wBACtC,uBAAuB,QAAQ,qBAAqB;wBACpD,aAAa,QAAQ,WAAW;wBAChC,gBAAgB,WAAW,OAAO,EAAE,kBAAkB,EAAE;wBACxD,OAAO;wBACP,aAAa;oBACf;gBACF;YACF;QACF,GACA,gBAAgB;QAChB,CAAC,cAAsB;YACrB,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY;gBACZ,MAAM,UAAU,QAAQ,CAAC,aAAa;gBACtC,MAAM,WAAW,CAAC,QAAQ,EAAE,QAAQ,aAAa,CAAC,cAAc,CAAC;gBACjE,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,WAAW;gBACtC,8IAAW,CAAC,SAAS,CAAC,UAAU,OAAO,OAAO;YAChD;YAEA,aAAa;YACb,MAAM,aAAa,iIAAK,CAAC,OAAO,CAAC;YACjC,IAAI,YAAY,WAAW,QAAQ;gBACjC,MAAM,iBAAiB;uBAAK,WAAW,OAAO,CAAC,cAAc,IAAI,EAAE;iBAAE;gBACrE,cAAc,CAAC,aAAa,GAAG;oBAC7B,eAAe,OAAO,OAAO,EAAE,iBAAiB,eAAe;oBAC/D,cAAc,OAAO,OAAO,EAAE,gBAAgB,CAAC,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC;oBACrE,SAAS,OAAO,OAAO;oBACvB,OAAO,OAAO,KAAK;oBACnB,YAAY,OAAO,OAAO,EAAE;oBAC5B,YAAY,OAAO,OAAO,EAAE;oBAC5B,gBAAgB,OAAO,OAAO,EAAE;oBAChC,aAAa,IAAI,OAAO,WAAW;gBACrC;gBAEA,iIAAK,CAAC,MAAM,CAAC,OAAO;oBAClB,SAAS;wBACP,GAAG,WAAW,OAAO;wBACrB;wBACA,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,KAAK,CAAC,EAAE,OAAO,EAAE,MAAM;oBACpE;gBACF;YACF;QACF,GACA,aACA,OACA,KAAK,WAAW;;QAGlB,OAAO;QACP,IAAI,eAAe;QACnB,IAAI,kBAAkB;QACtB,MAAM,gBAQD,EAAE;QAEP,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,UAAU,QAAQ,CAAC,EAAE;YAE3B,iCAAiC;YACjC,IAAI,UAAU,OAAO,OAAO,EAAE;gBAC5B;YACF;YAEA,IAAI,QAAQ,SAAS,YAAY;gBAC/B,mBAAmB,OAAO,OAAO,CAAC,UAAU;YAC9C;YAEA,cAAc,IAAI,CAAC;gBACjB,eAAe,QAAQ,aAAa;gBACpC,cAAc,QAAQ,KAAK;gBAC3B,SAAS,SAAS,OAAO,OAAO,GAAG;gBACnC,OAAO,QAAQ,SAAS;gBACxB,YAAY,QAAQ,SAAS;gBAC7B,YAAY,QAAQ,SAAS;gBAC7B,gBAAgB,QAAQ,SAAS;YACnC;QACF;QAEA,YAAY;QACZ,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,WAAW;QACzC,MAAM,sBAAsB,KAAK,GAAG,KAAK;QAEzC,MAAM,cAAc,KAAK,SAAS,CAAC;YACjC;YACA;YACA;YACA,eAAe,SAAS,MAAM;YAC9B;YACA,aAAa,SAAS,MAAM,GAAG;YAC/B;YACA;YACA,uBAAuB,sBAAsB,SAAS,MAAM;YAC5D,SAAS;YACT,aAAa,IAAI,OAAO,WAAW;YACnC,OAAO;YACP,aAAa;QACf,GAAG,MAAM;QACT,8IAAW,CAAC,SAAS,CAAC,aAAa;QAEnC,YAAY;QACZ,MAAM,aAAa,iIAAK,CAAC,OAAO,CAAC;QACjC,MAAM,cAAc,eAAe,IAAI,cAAc;QAErD,iIAAK,CAAC,MAAM,CAAC,OAAO;YAClB,QAAQ;YACR,UAAU;YACV,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,WAAW;YACxE,SAAS;gBACP,eAAe,SAAS,MAAM;gBAC9B,mBAAmB;gBACnB,gBAAgB,SAAS,MAAM,GAAG;gBAClC;gBACA;gBACA,uBAAuB,SAAS,MAAM,GAAG,IAAI,sBAAsB,SAAS,MAAM,GAAG;gBACrF,aAAa,YAAY,SAAS,eAAe,EAAE;gBACnD,gBAAgB;gBAChB,OAAO;gBACP,aAAa;YACf;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,iIAAK,CAAC,MAAM,CAAC,OAAO;YAClB,QAAQ;YACR,QAAQ,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACpE;IACF;AACF", "debugId": null}}]}