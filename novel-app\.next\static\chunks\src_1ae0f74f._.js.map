{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/NovelSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Novel } from '@/lib/database';\nimport { BookOpen, RefreshCw, Upload } from 'lucide-react';\n\ninterface NovelSelectorProps {\n  selectedNovel: Novel | null;\n  onNovelSelect: (novel: Novel | null) => void;\n  disabled?: boolean;\n}\n\ninterface NovelFile {\n  filename: string;\n  parsed: boolean;\n  novel: Novel | null;\n}\n\nexport default function NovelSelector({ selectedNovel, onNovelSelect, disabled }: NovelSelectorProps) {\n  const [novels, setNovels] = useState<Novel[]>([]);\n  const [availableFiles, setAvailableFiles] = useState<NovelFile[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [parsing, setParsing] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadNovels();\n  }, []);\n\n  const loadNovels = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/novels');\n      const result = await response.json();\n      \n      if (result.success) {\n        setNovels(result.data.novels);\n        setAvailableFiles(result.data.availableFiles);\n      } else {\n        console.error('加载小说列表失败:', result.error);\n      }\n    } catch (error) {\n      console.error('加载小说列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleParseNovel = async (filename: string, reparse = false) => {\n    setParsing(filename);\n    try {\n      const response = await fetch('/api/novels', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ filename, reparse }),\n      });\n\n      const result = await response.json();\n      \n      if (result.success) {\n        await loadNovels(); // 重新加载列表\n        alert(result.message);\n      } else {\n        alert(`解析失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('解析小说失败:', error);\n      alert('解析小说失败');\n    } finally {\n      setParsing(null);\n    }\n  };\n\n  const handleNovelSelect = (novel: Novel) => {\n    if (disabled) return;\n    onNovelSelect(novel);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h2 className=\"text-lg font-semibold text-gray-800 flex items-center\">\n          <BookOpen className=\"mr-2\" size={18} />\n          选择小说\n        </h2>\n        <button\n          onClick={loadNovels}\n          disabled={loading || disabled}\n          className=\"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n          title=\"刷新列表\"\n        >\n          <RefreshCw className={`${loading ? 'animate-spin' : ''}`} size={16} />\n        </button>\n      </div>\n\n      {loading ? (\n        <div className=\"text-center py-8 text-gray-500\">\n          加载中...\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {/* 已解析的小说 */}\n          {novels.length > 0 && (\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">已解析的小说</h3>\n              <div className=\"space-y-1\">\n                {novels.map((novel) => (\n                  <div\n                    key={novel.id}\n                    onClick={() => handleNovelSelect(novel)}\n                    className={`p-2 border rounded cursor-pointer transition-colors ${\n                      selectedNovel?.id === novel.id\n                        ? 'border-blue-500 bg-blue-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n                  >\n                    <div className=\"font-medium text-gray-800 text-sm\">{novel.title}</div>\n                    <div className=\"text-xs text-gray-500\">\n                      {novel.chapterCount || 0} 章节 • {novel.filename}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* 未解析的文件 */}\n          {availableFiles.filter(file => !file.parsed).length > 0 && (\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">未解析的文件</h3>\n              <div className=\"space-y-1\">\n                {availableFiles\n                  .filter(file => !file.parsed)\n                  .map((file) => (\n                    <div\n                      key={file.filename}\n                      className=\"p-2 border border-gray-200 rounded bg-gray-50\"\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"min-w-0 flex-1\">\n                          <div className=\"font-medium text-gray-800 text-sm truncate\">{file.filename}</div>\n                          <div className=\"text-xs text-gray-500\">未解析</div>\n                        </div>\n                        <button\n                          onClick={() => handleParseNovel(file.filename)}\n                          disabled={parsing === file.filename || disabled}\n                          className=\"flex items-center px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 ml-2\"\n                        >\n                          {parsing === file.filename ? (\n                            <>\n                              <RefreshCw className=\"animate-spin mr-1\" size={12} />\n                              解析中\n                            </>\n                          ) : (\n                            <>\n                              <Upload className=\"mr-1\" size={12} />\n                              解析\n                            </>\n                          )}\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n              </div>\n            </div>\n          )}\n\n          {availableFiles.length === 0 && (\n            <div className=\"text-center py-6 text-gray-500\">\n              <BookOpen className=\"mx-auto mb-2\" size={32} />\n              <p className=\"text-sm\">novels 文件夹中没有找到小说文件</p>\n              <p className=\"text-xs\">请将 .txt 或 .md 文件放入 novels 文件夹</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {selectedNovel && (\n        <div className=\"mt-3 p-2 bg-blue-50 border border-blue-200 rounded\">\n          <div className=\"text-sm text-blue-800\">\n            <strong>已选择:</strong> {selectedNovel.title}\n          </div>\n          <div className=\"text-xs text-blue-600\">\n            {selectedNovel.chapterCount || 0} 章节\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;AAAA;;;AAJA;;;AAkBe,SAAS,cAAc,KAA8D;QAA9D,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAsB,GAA9D;;IACpC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAU,EAAE;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAc,EAAE;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAgB;IAEtD,IAAA,0KAAS;mCAAC;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,IAAI,CAAC,MAAM;gBAC5B,kBAAkB,OAAO,IAAI,CAAC,cAAc;YAC9C,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,eAAO;YAAkB,2EAAU;QAC1D,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAU;gBAAQ;YAC3C;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,cAAc,SAAS;gBAC7B,MAAM,OAAO,OAAO;YACtB,OAAO;gBACL,MAAM,AAAC,SAAqB,OAAb,OAAO,KAAK;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU;QACd,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,6NAAQ;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGzC,6LAAC;wBACC,SAAS;wBACT,UAAU,WAAW;wBACrB,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,gOAAS;4BAAC,WAAW,AAAC,GAAgC,OAA9B,UAAU,iBAAiB;4BAAM,MAAM;;;;;;;;;;;;;;;;;YAInE,wBACC,6LAAC;gBAAI,WAAU;0BAAiC;;;;;qCAIhD,6LAAC;gBAAI,WAAU;;oBAEZ,OAAO,MAAM,GAAG,mBACf,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;wCAEC,SAAS,IAAM,kBAAkB;wCACjC,WAAW,AAAC,uDAIR,OAHF,CAAA,0BAAA,oCAAA,cAAe,EAAE,MAAK,MAAM,EAAE,GAC1B,+BACA,yCACL,KAAmD,OAAhD,WAAW,kCAAkC;;0DAEjD,6LAAC;gDAAI,WAAU;0DAAqC,MAAM,KAAK;;;;;;0DAC/D,6LAAC;gDAAI,WAAU;;oDACZ,MAAM,YAAY,IAAI;oDAAE;oDAAO,MAAM,QAAQ;;;;;;;;uCAV3C,MAAM,EAAE;;;;;;;;;;;;;;;;oBAmBtB,eAAe,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,MAAM,EAAE,MAAM,GAAG,mBACpD,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,eACE,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,MAAM,EAC3B,GAAG,CAAC,CAAC,qBACJ,6LAAC;wCAEC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA8C,KAAK,QAAQ;;;;;;sEAC1E,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDACC,SAAS,IAAM,iBAAiB,KAAK,QAAQ;oDAC7C,UAAU,YAAY,KAAK,QAAQ,IAAI;oDACvC,WAAU;8DAET,YAAY,KAAK,QAAQ,iBACxB;;0EACE,6LAAC,gOAAS;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;qFAIvD;;0EACE,6LAAC,mNAAM;gEAAC,WAAU;gEAAO,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;uCApBxC,KAAK,QAAQ;;;;;;;;;;;;;;;;oBAgC7B,eAAe,MAAM,KAAK,mBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6NAAQ;gCAAC,WAAU;gCAAe,MAAM;;;;;;0CACzC,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;;;;;;;YAM9B,+BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAO;;;;;;4BAAa;4BAAE,cAAc,KAAK;;;;;;;kCAE5C,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,YAAY,IAAI;4BAAE;;;;;;;;;;;;;;;;;;;AAM7C;GA5KwB;KAAA", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/ChapterSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Novel, Chapter } from '@/lib/database';\nimport { FileText, Info } from 'lucide-react';\n\ninterface ChapterSelectorProps {\n  novel: Novel | null;\n  selectedChapters: string;\n  onChaptersChange: (chapters: string) => void;\n  disabled?: boolean;\n}\n\nexport default function ChapterSelector({ \n  novel, \n  selectedChapters, \n  onChaptersChange, \n  disabled \n}: ChapterSelectorProps) {\n  const [chapters, setChapters] = useState<Chapter[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [previewChapters, setPreviewChapters] = useState<number[]>([]);\n\n  useEffect(() => {\n    if (novel) {\n      loadChapters(novel.id);\n    } else {\n      setChapters([]);\n      setPreviewChapters([]);\n    }\n  }, [novel]);\n\n  useEffect(() => {\n    // 解析章节范围并预览\n    if (selectedChapters && chapters.length > 0) {\n      const parsed = parseChapterRange(selectedChapters, chapters.length);\n      setPreviewChapters(parsed);\n    } else {\n      setPreviewChapters([]);\n    }\n  }, [selectedChapters, chapters]);\n\n  const loadChapters = async (novelId: string) => {\n    setLoading(true);\n    try {\n      const response = await fetch(`/api/chapters?novelId=${novelId}`);\n      const result = await response.json();\n      \n      if (result.success) {\n        setChapters(result.data);\n      } else {\n        console.error('加载章节列表失败:', result.error);\n      }\n    } catch (error) {\n      console.error('加载章节列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const parseChapterRange = (rangeStr: string, maxChapter: number): number[] => {\n    const chapters: number[] = [];\n    const parts = rangeStr.split(',').map(part => part.trim());\n    \n    for (const part of parts) {\n      if (part.includes('-')) {\n        // 范围格式 (例如: \"1-5\")\n        const [start, end] = part.split('-').map(num => parseInt(num.trim()));\n        if (!isNaN(start) && !isNaN(end) && start <= end) {\n          for (let i = start; i <= Math.min(end, maxChapter); i++) {\n            if (i > 0 && !chapters.includes(i)) {\n              chapters.push(i);\n            }\n          }\n        }\n      } else {\n        // 单个章节\n        const chapterNum = parseInt(part);\n        if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {\n          chapters.push(chapterNum);\n        }\n      }\n    }\n    \n    return chapters.sort((a, b) => a - b);\n  };\n\n  const handleQuickSelect = (type: 'all' | 'first10' | 'last10') => {\n    if (disabled || chapters.length === 0) return;\n\n    let range = '';\n    switch (type) {\n      case 'all':\n        range = `1-${chapters.length}`;\n        break;\n      case 'first10':\n        range = `1-${Math.min(10, chapters.length)}`;\n        break;\n      case 'last10':\n        range = `${Math.max(1, chapters.length - 9)}-${chapters.length}`;\n        break;\n    }\n    onChaptersChange(range);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <h2 className=\"text-lg font-semibold text-gray-800 mb-3 flex items-center\">\n        <FileText className=\"mr-2\" size={18} />\n        选择章节 {novel && <span className=\"text-sm text-gray-500 ml-2\">共 {chapters.length} 章</span>}\n      </h2>\n\n      {!novel ? (\n        <div className=\"text-center py-6 text-gray-500\">\n          <FileText className=\"mx-auto mb-2\" size={32} />\n          <p className=\"text-sm\">请先选择一部小说</p>\n        </div>\n      ) : loading ? (\n        <div className=\"text-center py-6 text-gray-500 text-sm\">\n          加载中...\n        </div>\n      ) : (\n        <div className=\"space-y-3\">\n          {/* 章节范围输入 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              章节范围\n            </label>\n            <input\n              type=\"text\"\n              value={selectedChapters}\n              onChange={(e) => onChaptersChange(e.target.value)}\n              disabled={disabled}\n              placeholder=\"例如: 1-5,7,10-12\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n            />\n            <div className=\"mt-1 flex items-center text-xs text-gray-500\">\n              <Info className=\"mr-1\" size={12} />\n              支持范围(1-5)、单个章节(7)、组合(1-5,7,10-12)\n            </div>\n          </div>\n\n          {/* 快速选择按钮 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              快速选择\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => handleQuickSelect('all')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                全部章节 (1-{chapters.length})\n              </button>\n              <button\n                onClick={() => handleQuickSelect('first10')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                前10章\n              </button>\n              <button\n                onClick={() => handleQuickSelect('last10')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                后10章\n              </button>\n            </div>\n          </div>\n\n          {/* 章节预览 */}\n          {previewChapters.length > 0 && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                将要改写的章节 ({previewChapters.length} 章)\n              </label>\n              <div className=\"max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50\">\n                <div className=\"grid grid-cols-1 gap-1 text-sm\">\n                  {previewChapters.map((chapterNum) => {\n                    const chapter = chapters.find(ch => ch.chapterNumber === chapterNum);\n                    return (\n                      <div key={chapterNum} className=\"flex items-center\">\n                        <span className=\"font-medium text-blue-600 w-12\">\n                          第{chapterNum}章\n                        </span>\n                        <span className=\"text-gray-700 truncate\">\n                          {chapter?.title || '未知标题'}\n                        </span>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 章节列表 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              所有章节 ({chapters.length} 章)\n            </label>\n            <div className=\"max-h-60 overflow-y-auto border border-gray-200 rounded-md\">\n              {chapters.map((chapter) => (\n                <div\n                  key={chapter.id}\n                  className={`p-2 border-b border-gray-100 last:border-b-0 ${\n                    previewChapters.includes(chapter.chapterNumber)\n                      ? 'bg-blue-50 border-l-4 border-l-blue-500'\n                      : 'hover:bg-gray-50'\n                  }`}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"font-medium text-gray-800 truncate\">\n                        第{chapter.chapterNumber}章 {chapter.title}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        {chapter.content.length} 字符\n                      </div>\n                    </div>\n                    {previewChapters.includes(chapter.chapterNumber) && (\n                      <div className=\"ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\">\n                        已选择\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;;;AAJA;;;AAae,SAAS,gBAAgB,KAKjB;QALiB,EACtC,KAAK,EACL,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACa,GALiB;;IAMtC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,yKAAQ,EAAW,EAAE;IAEnE,IAAA,0KAAS;qCAAC;YACR,IAAI,OAAO;gBACT,aAAa,MAAM,EAAE;YACvB,OAAO;gBACL,YAAY,EAAE;gBACd,mBAAmB,EAAE;YACvB;QACF;oCAAG;QAAC;KAAM;IAEV,IAAA,0KAAS;qCAAC;YACR,YAAY;YACZ,IAAI,oBAAoB,SAAS,MAAM,GAAG,GAAG;gBAC3C,MAAM,SAAS,kBAAkB,kBAAkB,SAAS,MAAM;gBAClE,mBAAmB;YACrB,OAAO;gBACL,mBAAmB,EAAE;YACvB;QACF;oCAAG;QAAC;QAAkB;KAAS;IAE/B,MAAM,eAAe,OAAO;QAC1B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,yBAAgC,OAAR;YACtD,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,OAAO,IAAI;YACzB,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,UAAkB;QAC3C,MAAM,WAAqB,EAAE;QAC7B,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAEvD,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,QAAQ,CAAC,MAAM;gBACtB,mBAAmB;gBACnB,MAAM,CAAC,OAAO,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,SAAS,IAAI,IAAI;gBACjE,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,SAAS,KAAK;oBAChD,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,GAAG,CAAC,KAAK,aAAa,IAAK;wBACvD,IAAI,IAAI,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAI;4BAClC,SAAS,IAAI,CAAC;wBAChB;oBACF;gBACF;YACF,OAAO;gBACL,OAAO;gBACP,MAAM,aAAa,SAAS;gBAC5B,IAAI,CAAC,MAAM,eAAe,aAAa,KAAK,cAAc,cAAc,CAAC,SAAS,QAAQ,CAAC,aAAa;oBACtG,SAAS,IAAI,CAAC;gBAChB;YACF;QACF;QAEA,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACrC;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY,SAAS,MAAM,KAAK,GAAG;QAEvC,IAAI,QAAQ;QACZ,OAAQ;YACN,KAAK;gBACH,QAAQ,AAAC,KAAoB,OAAhB,SAAS,MAAM;gBAC5B;YACF,KAAK;gBACH,QAAQ,AAAC,KAAkC,OAA9B,KAAK,GAAG,CAAC,IAAI,SAAS,MAAM;gBACzC;YACF,KAAK;gBACH,QAAQ,AAAC,GAAsC,OAApC,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,IAAG,KAAmB,OAAhB,SAAS,MAAM;gBAC9D;QACJ;QACA,iBAAiB;IACnB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC,6NAAQ;wBAAC,WAAU;wBAAO,MAAM;;;;;;oBAAM;oBACjC,uBAAS,6LAAC;wBAAK,WAAU;;4BAA6B;4BAAG,SAAS,MAAM;4BAAC;;;;;;;;;;;;;YAGhF,CAAC,sBACA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6NAAQ;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCACzC,6LAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;uBAEvB,wBACF,6LAAC;gBAAI,WAAU;0BAAyC;;;;;qCAIxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,UAAU;gCACV,aAAY;gCACZ,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAI;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;;;;;;;kCAMvC,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;;4CACX;4CACU,SAAS,MAAM;4CAAC;;;;;;;kDAE3B,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;;oBAOJ,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAA+C;oCACpD,gBAAgB,MAAM;oCAAC;;;;;;;0CAEnC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC;wCACpB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,aAAa,KAAK;wCACzD,qBACE,6LAAC;4CAAqB,WAAU;;8DAC9B,6LAAC;oDAAK,WAAU;;wDAAiC;wDAC7C;wDAAW;;;;;;;8DAEf,6LAAC;oDAAK,WAAU;8DACb,CAAA,oBAAA,8BAAA,QAAS,KAAK,KAAI;;;;;;;2CALb;;;;;oCASd;;;;;;;;;;;;;;;;;kCAOR,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAA+C;oCACvD,SAAS,MAAM;oCAAC;;;;;;;0CAEzB,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAEC,WAAW,AAAC,gDAIX,OAHC,gBAAgB,QAAQ,CAAC,QAAQ,aAAa,IAC1C,4CACA;kDAGN,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEAAqC;gEAChD,QAAQ,aAAa;gEAAC;gEAAG,QAAQ,KAAK;;;;;;;sEAE1C,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,OAAO,CAAC,MAAM;gEAAC;;;;;;;;;;;;;gDAG3B,gBAAgB,QAAQ,CAAC,QAAQ,aAAa,mBAC7C,6LAAC;oDAAI,WAAU;8DAA2D;;;;;;;;;;;;uCAjBzE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BjC;GA/NwB;KAAA", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/gemini.ts"], "sourcesContent": ["// Gemini API 集成 - 多Key池管理\nconst API_KEYS = [\n  {\n    key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw', // My First Project - 4倍强度\n    name: 'My First Project',\n    weight: 1, // 权重，表示相对强度\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0, // 冷却时间\n  },\n  {\n    key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y', // ankibot\n    name: 'ankibot',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY', // Generative Language Client\n    name: 'Generative Language Client',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc', // In The Novel\n    name: 'In The Novel',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk', // chat\n    name: 'chat',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  }\n];\n\n// API配置\nconst getGeminiApiUrl = (model: string = 'gemini-2.5-flash-lite') =>\n  `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;\nconst REQUEST_DELAY = 1000; // 请求间隔（毫秒）\nconst COOLDOWN_DURATION = 60000; // 429错误后的冷却时间（毫秒）\nconst MAX_RETRIES = 5; // 增加最大重试次数\nconst EXPONENTIAL_BACKOFF_BASE = 2000; // 指数退避基础时间（毫秒）\nconst MAX_WAIT_TIME = 30000; // 最大等待时间（毫秒）\n\n// API Key管理类\nclass ApiKeyManager {\n  private keys = [...API_KEYS];\n\n  // 获取最佳可用的API Key\n  getBestAvailableKey() {\n    const now = Date.now();\n\n    // 过滤掉冷却中的key\n    const availableKeys = this.keys.filter(key => key.cooldownUntil <= now);\n\n    if (availableKeys.length === 0) {\n      // 如果所有key都在冷却中，返回冷却时间最短的\n      return this.keys.reduce((min, key) =>\n        key.cooldownUntil < min.cooldownUntil ? key : min\n      );\n    }\n\n    // 根据权重和使用频率选择最佳key\n    const bestKey = availableKeys.reduce((best, key) => {\n      const keyScore = key.weight / (key.requestCount + 1);\n      const bestScore = best.weight / (best.requestCount + 1);\n      return keyScore > bestScore ? key : best;\n    });\n\n    return bestKey;\n  }\n\n  // 记录API使用\n  recordUsage(keyName: string, success: boolean) {\n    const key = this.keys.find(k => k.name === keyName);\n    if (key) {\n      key.requestCount++;\n      key.lastUsed = Date.now();\n\n      if (!success) {\n        // 如果失败，设置冷却时间\n        key.cooldownUntil = Date.now() + COOLDOWN_DURATION;\n      }\n    }\n  }\n\n  // 获取统计信息\n  getStats() {\n    return this.keys.map(key => ({\n      name: key.name,\n      requestCount: key.requestCount,\n      weight: key.weight,\n      isAvailable: key.cooldownUntil <= Date.now(),\n      cooldownRemaining: Math.max(0, key.cooldownUntil - Date.now()),\n    }));\n  }\n}\n\nconst keyManager = new ApiKeyManager();\n\nexport interface RewriteRequest {\n  originalText: string;\n  rules: string;\n  chapterTitle?: string;\n  chapterNumber?: number;\n  model?: string;\n  // 上下文信息\n  novelContext?: {\n    summary: string;\n    mainCharacters: Array<{\n      name: string;\n      role: string;\n      description: string;\n      relationships?: string;\n    }>;\n    worldSetting: string;\n    writingStyle: string;\n    tone: string;\n  };\n  chapterContext?: {\n    previousChapterSummary?: string;\n    keyEvents: string[];\n    characterStates: Array<{\n      name: string;\n      status: string;\n      emotions: string;\n      relationships: string;\n    }>;\n    plotProgress: string;\n    contextualNotes: string;\n  };\n}\n\nexport interface RewriteResponse {\n  rewrittenText: string;\n  success: boolean;\n  error?: string;\n  apiKeyUsed?: string;\n  tokensUsed?: number;\n  model?: string;\n  processingTime?: number;\n  detailedError?: string; // 新增详细错误信息\n  retryCount?: number; // 新增重试次数记录\n  debugInfo?: any; // 新增调试信息\n}\n\n// 构建改写提示词\nfunction buildPrompt(request: RewriteRequest): string {\n  const { originalText, rules, chapterTitle, chapterNumber, novelContext, chapterContext } = request;\n\n  let prompt = `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：\n\n改写规则：\n${rules}\n\n${chapterTitle ? `当前章节：${chapterTitle}` : ''}`;\n\n  // 添加小说整体上下文\n  if (novelContext) {\n    prompt += `\n\n【小说背景信息】\n小说摘要：${novelContext.summary}\n\n主要人物：\n${novelContext.mainCharacters.map(char =>\n      `- ${char.name}(${char.role}): ${char.description}${char.relationships ? ` | 关系：${char.relationships}` : ''}`\n    ).join('\\n')}\n\n世界观设定：${novelContext.worldSetting}\n\n写作风格：${novelContext.writingStyle}\n\n整体语调：${novelContext.tone}`;\n  }\n\n  // 添加章节上下文\n  if (chapterContext) {\n    prompt += `\n\n【章节上下文信息】`;\n\n    if (chapterContext.previousChapterSummary) {\n      prompt += `\n前一章摘要：${chapterContext.previousChapterSummary}`;\n    }\n\n    if (chapterContext.keyEvents.length > 0) {\n      prompt += `\n本章关键事件：${chapterContext.keyEvents.join('、')}`;\n    }\n\n    if (chapterContext.characterStates.length > 0) {\n      prompt += `\n人物状态：\n${chapterContext.characterStates.map(state =>\n        `- ${state.name}: ${state.status} | 情感：${state.emotions} | 关系：${state.relationships}`\n      ).join('\\n')}`;\n    }\n\n    prompt += `\n情节推进：${chapterContext.plotProgress}`;\n\n    if (chapterContext.contextualNotes) {\n      prompt += `\n重要注释：${chapterContext.contextualNotes}`;\n    }\n  }\n\n  prompt += `\n\n原文内容：\n${originalText}\n\n请严格按照改写规则进行改写，同时保持与小说整体背景和上下文的连贯性。改写后的内容应该：\n1. 遵循所有指定的改写规则\n2. 保持与小说整体背景的一致性\n3. 确保人物性格和关系的连贯性\n4. 保持情节发展的逻辑性\n5. 维持原有的写作风格和语调\n6. 确保文字流畅自然\n\n请直接输出改写后的内容，不要添加任何解释或说明：`;\n\n  return prompt;\n}\n\n// 调用Gemini API进行文本改写 - 增强错误处理和重试机制\nexport async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {\n  const startTime = Date.now();\n  let lastError = '';\n  let lastDetailedError = '';\n  let requestPayload: any = null; // 保存请求数据用于调试\n\n  for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {\n    try {\n      const apiKey = keyManager.getBestAvailableKey();\n\n      // 智能等待策略：如果key在冷却中，使用指数退避\n      if (apiKey.cooldownUntil > Date.now()) {\n        const cooldownWait = Math.min(apiKey.cooldownUntil - Date.now(), MAX_WAIT_TIME);\n        const exponentialWait = Math.min(EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt), MAX_WAIT_TIME);\n        const waitTime = Math.max(cooldownWait, exponentialWait);\n\n        console.log(`等待 ${waitTime}ms (尝试 ${attempt + 1}/${MAX_RETRIES}, API Key: ${apiKey.name})`);\n        await new Promise(resolve => setTimeout(resolve, waitTime));\n      }\n\n      const prompt = buildPrompt(request);\n\n      // 构建请求数据并保存用于调试\n      requestPayload = {\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }],\n        generationConfig: {\n          temperature: 0.6,\n          topK: 10,\n          topP: 0.8,\n          \"thinkingConfig\": {\n            \"thinkingBudget\": 0\n          }\n        },\n      };\n\n      // 增加请求超时设置\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时\n\n      const apiUrl = getGeminiApiUrl(request.model);\n      const response = await fetch(`${apiUrl}?key=${apiKey.key}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestPayload),\n        signal: controller.signal,\n      });\n\n      clearTimeout(timeoutId);\n      const processingTime = Date.now() - startTime;\n\n      // 处理429错误（API限流）\n      if (response.status === 429) {\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API限流 (${apiKey.name})`;\n        lastDetailedError = `第${attempt + 1}次尝试: API Key \"${apiKey.name}\" 遇到限流，状态码: 429`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);\n          console.log(`API限流，${retryDelay}ms后重试...`);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n      }\n\n      // 处理其他HTTP错误\n      if (!response.ok) {\n        const errorData = await response.text();\n        console.error('Gemini API error:', errorData);\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API请求失败: ${response.status} ${response.statusText}`;\n        lastDetailedError = `第${attempt + 1}次尝试: HTTP ${response.status} ${response.statusText}, 响应: ${errorData.substring(0, 200)}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n        };\n      }\n\n      // 解析响应数据\n      let data;\n      try {\n        data = await response.json();\n      } catch (parseError) {\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = 'JSON解析失败';\n        lastDetailedError = `第${attempt + 1}次尝试: 无法解析API响应为JSON, 错误: ${parseError instanceof Error ? parseError.message : '未知错误'}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      // 记录成功使用\n      keyManager.recordUsage(apiKey.name, true);\n\n      // 增强响应验证\n      if (!data.candidates || data.candidates.length === 0) {\n        // 详细的调试信息\n        const debugInfo = {\n          chapterInfo: {\n            number: request.chapterNumber,\n            title: request.chapterTitle,\n            contentLength: request.originalText?.length,\n            model: request.model\n          },\n          requestInfo: {\n            promptLength: prompt.length,\n            apiUrl: apiUrl,\n            apiKeyName: apiKey.name\n          },\n          responseInfo: {\n            status: response.status,\n            statusText: response.statusText,\n            hasData: !!data,\n            dataKeys: data ? Object.keys(data) : [],\n            candidates: data?.candidates,\n            candidatesLength: data?.candidates?.length,\n            fullResponse: JSON.stringify(data, null, 2)\n          },\n          requestPayload: {\n            contentsLength: requestPayload?.contents?.[0]?.parts?.[0]?.text?.length,\n            generationConfig: requestPayload?.generationConfig\n          }\n        };\n\n        console.error('🔍 Gemini API 调试信息:', debugInfo);\n\n        lastError = '没有收到有效的响应内容';\n        lastDetailedError = `第${attempt + 1}次尝试: API响应中没有candidates字段或为空数组\n章节信息: ${request.chapterTitle} (第${request.chapterNumber}章)\n内容长度: ${request.originalText?.length} 字符\n提示词长度: ${prompt.length} 字符\nAPI响应: ${JSON.stringify(data).substring(0, 1000)}\n完整调试信息已输出到控制台`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          keyManager.recordUsage(apiKey.name, false); // 标记为失败，触发冷却\n          const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n          debugInfo: debugInfo, // 添加调试信息到返回结果\n        };\n      }\n\n      const candidate = data.candidates[0];\n\n      if (candidate.finishReason === 'SAFETY') {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '内容被安全过滤器拦截，请调整改写规则或原文内容',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: `内容被安全过滤器拦截，finishReason: SAFETY`,\n          retryCount: attempt + 1,\n        };\n      }\n\n      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {\n        lastError = '响应内容格式错误';\n        lastDetailedError = `第${attempt + 1}次尝试: candidate内容格式错误, candidate: ${JSON.stringify(candidate).substring(0, 300)}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          keyManager.recordUsage(apiKey.name, false);\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      const rewrittenText = candidate.content.parts[0].text;\n\n      // 验证生成的内容质量\n      if (!rewrittenText || rewrittenText.trim().length < 10) {\n        lastError = '生成的内容过短或为空';\n        lastDetailedError = `第${attempt + 1}次尝试: 生成的内容长度: ${rewrittenText?.length || 0}, 内容: \"${rewrittenText?.substring(0, 100) || 'null'}\"`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          keyManager.recordUsage(apiKey.name, false);\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      // 尝试从响应中提取token使用信息\n      const tokensUsed = data.usageMetadata?.totalTokenCount || 0;\n\n      return {\n        rewrittenText: rewrittenText.trim(),\n        success: true,\n        apiKeyUsed: apiKey.name,\n        tokensUsed,\n        model: request.model || 'gemini-2.5-flash-lite',\n        processingTime,\n        retryCount: attempt + 1,\n      };\n\n    } catch (error) {\n      console.error('Gemini API调用错误:', error);\n\n      // 处理不同类型的错误\n      if (error instanceof Error && error.name === 'AbortError') {\n        lastError = '请求超时';\n        lastDetailedError = `第${attempt + 1}次尝试: 请求超时 (60秒)`;\n      } else {\n        lastError = `网络错误: ${error instanceof Error ? error.message : '未知错误'}`;\n        lastDetailedError = `第${attempt + 1}次尝试: ${error instanceof Error ? error.stack || error.message : '未知网络错误'}`;\n      }\n\n      if (attempt < MAX_RETRIES - 1) {\n        const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);\n        console.log(`网络错误，${retryDelay}ms后重试...`);\n        await new Promise(resolve => setTimeout(resolve, retryDelay));\n      }\n    }\n  }\n\n  return {\n    rewrittenText: '',\n    success: false,\n    error: `重试${MAX_RETRIES}次后仍然失败: ${lastError}`,\n    processingTime: Date.now() - startTime,\n    detailedError: lastDetailedError,\n    retryCount: MAX_RETRIES,\n  };\n}\n\n// 改进的批量改写函数 - 支持实时写入、详细进度跟踪和失败恢复\nexport async function rewriteChapters(\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number, details?: any) => void,\n  onChapterComplete?: (chapterIndex: number, result: any) => void,\n  concurrency: number = 3, // 降低并发数以避免429错误\n  model: string = 'gemini-2.5-flash-lite', // 模型选择\n  enableFailureRecovery: boolean = true // 启用失败恢复机制\n): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {\n  const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);\n  let completed = 0;\n  let totalTokensUsed = 0;\n  const startTime = Date.now();\n\n  // 使用更保守的并发策略\n  const semaphore = new Semaphore(concurrency);\n\n  const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {\n    await semaphore.acquire();\n    const chapterStartTime = Date.now();\n\n    try {\n\n      const result = await rewriteText({\n        originalText: chapter.content,\n        rules,\n        chapterTitle: chapter.title,\n        chapterNumber: chapter.number,\n        model,\n      });\n\n      const chapterProcessingTime = Date.now() - chapterStartTime;\n\n      if (result.tokensUsed) {\n        totalTokensUsed += result.tokensUsed;\n      }\n\n      const chapterResult = {\n        success: result.success,\n        content: result.rewrittenText,\n        error: result.error,\n        details: {\n          apiKeyUsed: result.apiKeyUsed,\n          tokensUsed: result.tokensUsed,\n          model: result.model,\n          processingTime: chapterProcessingTime,\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n        }\n      };\n\n      results[index] = chapterResult;\n      completed++;\n\n      // 实时回调章节完成\n      if (onChapterComplete) {\n        onChapterComplete(index, chapterResult);\n      }\n\n      // 更新进度，包含详细信息\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            processingTime: chapterProcessingTime,\n            apiKey: result.apiKeyUsed,\n            tokens: result.tokensUsed,\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      // 添加请求间隔\n      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));\n\n      return result;\n    } catch (error) {\n      const chapterErrorTime = Date.now();\n      const errorResult = {\n        success: false,\n        content: '',\n        error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        details: {\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n          processingTime: chapterErrorTime - chapterStartTime,\n        }\n      };\n\n      results[index] = errorResult;\n      completed++;\n\n      if (onChapterComplete) {\n        onChapterComplete(index, errorResult);\n      }\n\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            error: error instanceof Error ? error.message : '未知错误',\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      return null;\n    } finally {\n      semaphore.release();\n    }\n  };\n\n  // 并发处理所有章节\n  const promises = chapters.map((chapter, index) => processChapter(chapter, index));\n  await Promise.all(promises);\n\n  // 失败恢复机制：对失败的章节进行额外重试\n  if (enableFailureRecovery) {\n    const failedChapters = results\n      .map((result, index) => ({ result, index, chapter: chapters[index] }))\n      .filter(item => !item.result.success);\n\n    if (failedChapters.length > 0) {\n      console.log(`开始恢复 ${failedChapters.length} 个失败的章节...`);\n\n      // 为失败恢复使用更保守的设置\n      const recoverySemaphore = new Semaphore(1); // 串行处理失败的章节\n\n      for (const { index, chapter } of failedChapters) {\n        await recoverySemaphore.acquire();\n\n        try {\n          console.log(`正在恢复第 ${chapter.number} 章: ${chapter.title}`);\n\n          // 等待更长时间再重试\n          await new Promise(resolve => setTimeout(resolve, 5000));\n\n          const recoveryResult = await rewriteText({\n            originalText: chapter.content,\n            rules,\n            chapterTitle: chapter.title,\n            chapterNumber: chapter.number,\n            model,\n          });\n\n          if (recoveryResult.success) {\n            console.log(`成功恢复第 ${chapter.number} 章`);\n\n            const recoveredChapterResult = {\n              success: true,\n              content: recoveryResult.rewrittenText,\n              error: undefined,\n              details: {\n                ...recoveryResult,\n                chapterNumber: chapter.number,\n                chapterTitle: chapter.title,\n                isRecovered: true, // 标记为恢复的章节\n              }\n            };\n\n            results[index] = recoveredChapterResult;\n            completed++;\n\n            // 通知章节恢复完成\n            if (onChapterComplete) {\n              onChapterComplete(index, recoveredChapterResult);\n            }\n\n            // 更新进度\n            if (onProgress) {\n              const progressDetails = {\n                completed,\n                total: chapters.length,\n                totalTokensUsed: totalTokensUsed + (recoveryResult.tokensUsed || 0),\n                totalTime: Date.now() - startTime,\n                averageTimePerChapter: (Date.now() - startTime) / completed,\n                apiKeyStats: keyManager.getStats(),\n                currentChapter: {\n                  number: chapter.number,\n                  title: chapter.title,\n                  processingTime: recoveryResult.processingTime,\n                  apiKey: recoveryResult.apiKeyUsed,\n                  tokens: recoveryResult.tokensUsed,\n                  isRecovered: true,\n                }\n              };\n\n              onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n            }\n          } else {\n            console.log(`第 ${chapter.number} 章恢复失败: ${recoveryResult.error}`);\n            // 更新失败信息，包含恢复尝试的详细信息\n            results[index] = {\n              ...results[index],\n              error: `原始失败: ${results[index].error}; 恢复失败: ${recoveryResult.error}`,\n              details: {\n                ...results[index].details,\n                recoveryAttempted: true,\n                recoveryError: recoveryResult.error,\n                recoveryDetailedError: recoveryResult.detailedError,\n              }\n            };\n          }\n        } catch (error) {\n          console.error(`恢复第 ${chapter.number} 章时发生异常:`, error);\n          results[index] = {\n            ...results[index],\n            error: `${results[index].error}; 恢复异常: ${error instanceof Error ? error.message : '未知错误'}`,\n            details: {\n              ...results[index].details,\n              recoveryAttempted: true,\n              recoveryException: error instanceof Error ? error.message : '未知错误',\n            }\n          };\n        } finally {\n          recoverySemaphore.release();\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\n// 信号量类，用于控制并发\nclass Semaphore {\n  private permits: number;\n  private waitQueue: Array<() => void> = [];\n\n  constructor(permits: number) {\n    this.permits = permits;\n  }\n\n  async acquire(): Promise<void> {\n    if (this.permits > 0) {\n      this.permits--;\n      return Promise.resolve();\n    }\n\n    return new Promise<void>((resolve) => {\n      this.waitQueue.push(resolve);\n    });\n  }\n\n  release(): void {\n    this.permits++;\n    if (this.waitQueue.length > 0) {\n      const resolve = this.waitQueue.shift();\n      if (resolve) {\n        this.permits--;\n        resolve();\n      }\n    }\n  }\n}\n\n// 测试API连接 - 增强版\nexport async function testGeminiConnection(): Promise<{\n  success: boolean;\n  error?: string;\n  details?: any;\n}> {\n  try {\n    const testResult = await rewriteText({\n      originalText: '这是一个测试文本。',\n      rules: '保持原文不变',\n    });\n\n    return {\n      success: testResult.success,\n      error: testResult.error,\n      details: {\n        apiKeyUsed: testResult.apiKeyUsed,\n        tokensUsed: testResult.tokensUsed,\n        model: testResult.model,\n        processingTime: testResult.processingTime,\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,\n      details: {\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  }\n}\n\n// 获取API Key使用统计\nexport function getApiKeyStats() {\n  return keyManager.getStats();\n}\n\n// 重置API Key统计\nexport function resetApiKeyStats() {\n  API_KEYS.forEach(key => {\n    key.requestCount = 0;\n    key.lastUsed = 0;\n    key.cooldownUntil = 0;\n  });\n}\n\n// 预设的改写规则模板\nexport let PRESET_RULES: Record<string, { name: string; description: string; rules: string }> = {\n  romance_focus: {\n    name: '感情戏增强',\n    description: '扩写男女主互动内容，对非感情戏部分一笔带过',\n    rules: `请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展`\n  },\n\n  character_fix: {\n    name: '人设修正',\n    description: '修正主角人设和对话风格',\n    rules: `请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式`\n  },\n\n  toxic_content_removal: {\n    name: '毒点清除',\n    description: '移除送女、绿帽等毒点情节',\n    rules: `请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容`\n  },\n\n  pacing_improvement: {\n    name: '节奏优化',\n    description: '优化故事节奏，删除拖沓内容',\n    rules: `请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性`\n  },\n\n  custom: {\n    name: '自定义规则',\n    description: '用户自定义的改写规则',\n    rules: ''\n  }\n};\n\n// 从数据库加载自定义预设并合并到 PRESET_RULES（仅在服务端使用）\nexport function loadCustomPresets() {\n  // 这个函数只在服务端使用，客户端组件应该通过 API 获取预设\n  if (typeof window !== 'undefined') {\n    console.warn('loadCustomPresets should not be called on client side');\n    return;\n  }\n\n  try {\n    const { presetDb } = require('@/lib/database');\n    const customPresets = presetDb.getAll();\n\n    // 将数据库中的预设添加到 PRESET_RULES\n    customPresets.forEach((preset: any) => {\n      PRESET_RULES[`custom_${preset.id}`] = {\n        name: preset.name,\n        description: preset.description,\n        rules: preset.rules\n      };\n    });\n  } catch (error) {\n    console.error('加载自定义预设失败:', error);\n  }\n}\n\n// 添加自定义预设规则（保持向后兼容）\nexport function addCustomPreset(name: string, description: string, rules: string): string {\n  const key = `custom_${Date.now()}`;\n  PRESET_RULES = {\n    ...PRESET_RULES,\n    [key]: {\n      name,\n      description,\n      rules\n    }\n  };\n  return key;\n}\n\n// 带上下文的重写函数（仅服务端使用）\nexport async function rewriteTextWithContext(\n  novelId: string,\n  chapterNumber: number,\n  originalText: string,\n  rules: string,\n  chapterTitle?: string,\n  model?: string\n): Promise<RewriteResponse> {\n  // 检查是否在服务端环境\n  if (typeof window !== 'undefined') {\n    console.warn('rewriteTextWithContext should only be used on server side');\n    // 在客户端环境下回退到普通重写\n    return await rewriteText({\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model\n    });\n  }\n\n  try {\n    // 动态导入避免循环依赖，只在服务端执行\n    const { novelContextDb, chapterContextDb } = require('./database');\n\n    // 获取小说整体上下文\n    const novelContext = novelContextDb.getByNovelId(novelId);\n\n    // 获取章节上下文\n    const chapterContext = chapterContextDb.getByChapter(novelId, chapterNumber);\n\n    // 构建请求\n    const request: RewriteRequest = {\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model,\n      novelContext: novelContext ? {\n        summary: novelContext.summary,\n        mainCharacters: novelContext.mainCharacters,\n        worldSetting: novelContext.worldSetting,\n        writingStyle: novelContext.writingStyle,\n        tone: novelContext.tone\n      } : undefined,\n      chapterContext: chapterContext ? {\n        previousChapterSummary: chapterContext.previousChapterSummary,\n        keyEvents: chapterContext.keyEvents,\n        characterStates: chapterContext.characterStates,\n        plotProgress: chapterContext.plotProgress,\n        contextualNotes: chapterContext.contextualNotes\n      } : undefined\n    };\n\n    return await rewriteText(request);\n  } catch (error) {\n    console.error('带上下文重写失败:', error);\n    // 如果获取上下文失败，回退到普通重写\n    return await rewriteText({\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model\n    });\n  }\n}\n"], "names": [], "mappings": "AAAA,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;AAC1B,MAAM,WAAW;IACf;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;CACD;AAED,QAAQ;AACR,MAAM,kBAAkB;QAAC,yEAAgB;WACvC,AAAC,2DAAgE,OAAN,OAAM;;AACnE,MAAM,gBAAgB,MAAM,WAAW;AACvC,MAAM,oBAAoB,OAAO,kBAAkB;AACnD,MAAM,cAAc,GAAG,WAAW;AAClC,MAAM,2BAA2B,MAAM,eAAe;AACtD,MAAM,gBAAgB,OAAO,aAAa;AAE1C,aAAa;AACb,MAAM;IAGJ,iBAAiB;IACjB,sBAAsB;QACpB,MAAM,MAAM,KAAK,GAAG;QAEpB,aAAa;QACb,MAAM,gBAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,aAAa,IAAI;QAEnE,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,yBAAyB;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,MAC5B,IAAI,aAAa,GAAG,IAAI,aAAa,GAAG,MAAM;QAElD;QAEA,mBAAmB;QACnB,MAAM,UAAU,cAAc,MAAM,CAAC,CAAC,MAAM;YAC1C,MAAM,WAAW,IAAI,MAAM,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC;YACnD,MAAM,YAAY,KAAK,MAAM,GAAG,CAAC,KAAK,YAAY,GAAG,CAAC;YACtD,OAAO,WAAW,YAAY,MAAM;QACtC;QAEA,OAAO;IACT;IAEA,UAAU;IACV,YAAY,OAAe,EAAE,OAAgB,EAAE;QAC7C,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAC3C,IAAI,KAAK;YACP,IAAI,YAAY;YAChB,IAAI,QAAQ,GAAG,KAAK,GAAG;YAEvB,IAAI,CAAC,SAAS;gBACZ,cAAc;gBACd,IAAI,aAAa,GAAG,KAAK,GAAG,KAAK;YACnC;QACF;IACF;IAEA,SAAS;IACT,WAAW;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3B,MAAM,IAAI,IAAI;gBACd,cAAc,IAAI,YAAY;gBAC9B,QAAQ,IAAI,MAAM;gBAClB,aAAa,IAAI,aAAa,IAAI,KAAK,GAAG;gBAC1C,mBAAmB,KAAK,GAAG,CAAC,GAAG,IAAI,aAAa,GAAG,KAAK,GAAG;YAC7D,CAAC;IACH;;QAjDA,+KAAQ,QAAO;eAAI;SAAS;;AAkD9B;AAEA,MAAM,aAAa,IAAI;AAgDvB,UAAU;AACV,SAAS,YAAY,OAAuB;IAC1C,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG;IAE3F,IAAI,SAAS,AAAC,6CAKd,OAFA,OAAM,QAEqC,OAA3C,eAAe,AAAC,QAAoB,OAAb,gBAAiB;IAExC,YAAY;IACZ,IAAI,cAAc;QAChB,UAAU,AAAC,sBAMb,OAHK,aAAa,OAAO,EAAC,eAOpB,OAJN,aAAa,cAAc,CAAC,GAAG,CAAC,CAAA,OAC5B,AAAC,KAAiB,OAAb,KAAK,IAAI,EAAC,KAAkB,OAAf,KAAK,IAAI,EAAC,OAAwB,OAAnB,KAAK,WAAW,EAA2D,OAAxD,KAAK,aAAa,GAAG,AAAC,SAA2B,OAAnB,KAAK,aAAa,IAAK,KACzG,IAAI,CAAC,OAAM,cAIV,OAFC,aAAa,YAAY,EAAC,aAI3B,OAFA,aAAa,YAAY,EAAC,aAER,OAAlB,aAAa,IAAI;IACtB;IAEA,UAAU;IACV,IAAI,gBAAgB;QAClB,UAAW;QAIX,IAAI,eAAe,sBAAsB,EAAE;YACzC,UAAU,AAAC,WAC6B,OAAtC,eAAe,sBAAsB;QACzC;QAEA,IAAI,eAAe,SAAS,CAAC,MAAM,GAAG,GAAG;YACvC,UAAU,AAAC,YAC2B,OAAnC,eAAe,SAAS,CAAC,IAAI,CAAC;QACnC;QAEA,IAAI,eAAe,eAAe,CAAC,MAAM,GAAG,GAAG;YAC7C,UAAU,AAAC,YAIE,OAFjB,eAAe,eAAe,CAAC,GAAG,CAAC,CAAA,QAC7B,AAAC,KAAmB,OAAf,MAAM,IAAI,EAAC,MAAyB,OAArB,MAAM,MAAM,EAAC,UAA+B,OAAvB,MAAM,QAAQ,EAAC,UAA4B,OAApB,MAAM,aAAa,GACnF,IAAI,CAAC;QACT;QAEA,UAAU,AAAC,UACoB,OAA5B,eAAe,YAAY;QAE9B,IAAI,eAAe,eAAe,EAAE;YAClC,UAAU,AAAC,UACqB,OAA/B,eAAe,eAAe;QACjC;IACF;IAEA,UAAU,AAAC,cAGE,OAAb,cAAa;IAYb,OAAO;AACT;AAGO,eAAe,YAAY,OAAuB;IACvD,MAAM,YAAY,KAAK,GAAG;IAC1B,IAAI,YAAY;IAChB,IAAI,oBAAoB;IACxB,IAAI,iBAAsB,MAAM,aAAa;IAE7C,IAAK,IAAI,UAAU,GAAG,UAAU,aAAa,UAAW;QACtD,IAAI;gBA6OiB;YA5OnB,MAAM,SAAS,WAAW,mBAAmB;YAE7C,0BAA0B;YAC1B,IAAI,OAAO,aAAa,GAAG,KAAK,GAAG,IAAI;gBACrC,MAAM,eAAe,KAAK,GAAG,CAAC,OAAO,aAAa,GAAG,KAAK,GAAG,IAAI;gBACjE,MAAM,kBAAkB,KAAK,GAAG,CAAC,2BAA2B,KAAK,GAAG,CAAC,GAAG,UAAU;gBAClF,MAAM,WAAW,KAAK,GAAG,CAAC,cAAc;gBAExC,QAAQ,GAAG,CAAC,AAAC,MAAuB,OAAlB,UAAS,WAAwB,OAAf,UAAU,GAAE,KAA4B,OAAzB,aAAY,eAAyB,OAAZ,OAAO,IAAI,EAAC;gBACxF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,MAAM,SAAS,YAAY;YAE3B,gBAAgB;YAChB,iBAAiB;gBACf,UAAU;oBAAC;wBACT,OAAO;4BAAC;gCACN,MAAM;4BACR;yBAAE;oBACJ;iBAAE;gBACF,kBAAkB;oBAChB,aAAa;oBACb,MAAM;oBACN,MAAM;oBACN,kBAAkB;wBAChB,kBAAkB;oBACpB;gBACF;YACF;YAEA,WAAW;YACX,MAAM,aAAa,IAAI;YACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,QAAQ,QAAQ;YAEvE,MAAM,SAAS,gBAAgB,QAAQ,KAAK;YAC5C,MAAM,WAAW,MAAM,MAAM,AAAC,GAAgB,OAAd,QAAO,SAAkB,OAAX,OAAO,GAAG,GAAI;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;gBACrB,QAAQ,WAAW,MAAM;YAC3B;YAEA,aAAa;YACb,MAAM,iBAAiB,KAAK,GAAG,KAAK;YAEpC,iBAAiB;YACjB,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;gBACpC,YAAY,AAAC,UAAqB,OAAZ,OAAO,IAAI,EAAC;gBAClC,oBAAoB,AAAC,IAA+B,OAA5B,UAAU,GAAE,kBAA4B,OAAZ,OAAO,IAAI,EAAC;gBAEhE,IAAI,UAAU,cAAc,GAAG;oBAC7B,MAAM,aAAa,2BAA2B,KAAK,GAAG,CAAC,GAAG;oBAC1D,QAAQ,GAAG,CAAC,AAAC,SAAmB,OAAX,YAAW;oBAChC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;YACF;YAEA,aAAa;YACb,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;gBACpC,YAAY,AAAC,YAA8B,OAAnB,SAAS,MAAM,EAAC,KAAuB,OAApB,SAAS,UAAU;gBAC9D,oBAAoB,AAAC,IAA2B,OAAxB,UAAU,GAAE,cAA+B,OAAnB,SAAS,MAAM,EAAC,KAA+B,OAA5B,SAAS,UAAU,EAAC,UAAoC,OAA5B,UAAU,SAAS,CAAC,GAAG;gBAEtH,IAAI,UAAU,cAAc,GAAG;oBAC7B,MAAM,aAAa,gBAAgB,CAAC,UAAU,CAAC;oBAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;oBACA,eAAe;gBACjB;YACF;YAEA,SAAS;YACT,IAAI;YACJ,IAAI;gBACF,OAAO,MAAM,SAAS,IAAI;YAC5B,EAAE,OAAO,YAAY;gBACnB,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;gBACpC,YAAY;gBACZ,oBAAoB,AAAC,IAA0C,OAAvC,UAAU,GAAE,6BAAqF,OAA1D,sBAAsB,QAAQ,WAAW,OAAO,GAAG;gBAElH,IAAI,UAAU,cAAc,GAAG;oBAC7B,MAAM,aAAa,gBAAgB,CAAC,UAAU,CAAC;oBAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;oBACA,eAAe;oBACf,YAAY,UAAU;gBACxB;YACF;YAEA,SAAS;YACT,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;YAEpC,SAAS;YACT,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;oBAMjC,uBAcG,kBAIF,uCAAA,kCAAA,iCAAA,2BAAA,0BAUpB;gBAjCA,UAAU;gBACV,MAAM,YAAY;oBAChB,aAAa;wBACX,QAAQ,QAAQ,aAAa;wBAC7B,OAAO,QAAQ,YAAY;wBAC3B,aAAa,GAAE,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,MAAM;wBAC3C,OAAO,QAAQ,KAAK;oBACtB;oBACA,aAAa;wBACX,cAAc,OAAO,MAAM;wBAC3B,QAAQ;wBACR,YAAY,OAAO,IAAI;oBACzB;oBACA,cAAc;wBACZ,QAAQ,SAAS,MAAM;wBACvB,YAAY,SAAS,UAAU;wBAC/B,SAAS,CAAC,CAAC;wBACX,UAAU,OAAO,OAAO,IAAI,CAAC,QAAQ,EAAE;wBACvC,UAAU,EAAE,iBAAA,2BAAA,KAAM,UAAU;wBAC5B,gBAAgB,EAAE,iBAAA,4BAAA,mBAAA,KAAM,UAAU,cAAhB,uCAAA,iBAAkB,MAAM;wBAC1C,cAAc,KAAK,SAAS,CAAC,MAAM,MAAM;oBAC3C;oBACA,gBAAgB;wBACd,cAAc,EAAE,2BAAA,sCAAA,2BAAA,eAAgB,QAAQ,cAAxB,gDAAA,4BAAA,wBAA0B,CAAC,EAAE,cAA7B,iDAAA,kCAAA,0BAA+B,KAAK,cAApC,uDAAA,mCAAA,+BAAsC,CAAC,EAAE,cAAzC,wDAAA,wCAAA,iCAA2C,IAAI,cAA/C,4DAAA,sCAAiD,MAAM;wBACvE,gBAAgB,EAAE,2BAAA,qCAAA,eAAgB,gBAAgB;oBACpD;gBACF;gBAEA,QAAQ,KAAK,CAAC,uBAAuB;gBAErC,YAAY;gBACZ,oBAAoB,AAzY5B,AAyY6B,IACrB,OADwB,UAAU,GAAE,0CACV,OAA1B,QAAQ,YAAY,EAAC,cAAK,QAAQ,aAAa,EAAC,cAE/C,QADD,yBAAA,QAAQ,YAAY,cAApB,6CAAA,uBAAsB,MAAM,EAAC,gBAE5B,OADA,OAAO,MAAM,EAAC,gBAC0B,OAAxC,KAAK,SAAS,CAAC,MAAM,SAAS,CAAC,GAAG,OAAM;gBAGzC,IAAI,UAAU,cAAc,GAAG;oBAC7B,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE,QAAQ,aAAa;oBACzD,MAAM,aAAa,2BAA2B,KAAK,GAAG,CAAC,GAAG;oBAC1D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;oBACA,eAAe;oBACf,YAAY,UAAU;oBACtB,WAAW;gBACb;YACF;YAEA,MAAM,YAAY,KAAK,UAAU,CAAC,EAAE;YAEpC,IAAI,UAAU,YAAY,KAAK,UAAU;gBACvC,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;oBACA,eAAgB;oBAChB,YAAY,UAAU;gBACxB;YACF;YAEA,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK,IAAI,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;gBAC1F,YAAY;gBACZ,oBAAoB,AAAC,IAAkD,OAA/C,UAAU,GAAE,qCAA+E,OAA5C,KAAK,SAAS,CAAC,WAAW,SAAS,CAAC,GAAG;gBAE9G,IAAI,UAAU,cAAc,GAAG;oBAC7B,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;oBACpC,MAAM,aAAa,gBAAgB,CAAC,UAAU,CAAC;oBAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;oBACA,eAAe;oBACf,YAAY,UAAU;gBACxB;YACF;YAEA,MAAM,gBAAgB,UAAU,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;YAErD,YAAY;YACZ,IAAI,CAAC,iBAAiB,cAAc,IAAI,GAAG,MAAM,GAAG,IAAI;gBACtD,YAAY;gBACZ,oBAAoB,AAAC,IAA+B,OAA5B,UAAU,GAAE,kBAAoD,OAApC,CAAA,0BAAA,oCAAA,cAAe,MAAM,KAAI,GAAE,WAAoD,OAA3C,CAAA,0BAAA,oCAAA,cAAe,SAAS,CAAC,GAAG,SAAQ,QAAO;gBAEnI,IAAI,UAAU,cAAc,GAAG;oBAC7B,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;oBACpC,MAAM,aAAa,gBAAgB,CAAC,UAAU,CAAC;oBAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;oBACA,eAAe;oBACf,YAAY,UAAU;gBACxB;YACF;YAEA,oBAAoB;YACpB,MAAM,aAAa,EAAA,sBAAA,KAAK,aAAa,cAAlB,0CAAA,oBAAoB,eAAe,KAAI;YAE1D,OAAO;gBACL,eAAe,cAAc,IAAI;gBACjC,SAAS;gBACT,YAAY,OAAO,IAAI;gBACvB;gBACA,OAAO,QAAQ,KAAK,IAAI;gBACxB;gBACA,YAAY,UAAU;YACxB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YAEjC,YAAY;YACZ,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD,YAAY;gBACZ,oBAAoB,AAAC,IAAe,OAAZ,UAAU,GAAE;YACtC,OAAO;gBACL,YAAY,AAAC,SAAwD,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,oBAAoB,AAAC,IAAsB,OAAnB,UAAU,GAAE,SAAwE,OAAjE,iBAAiB,QAAQ,MAAM,KAAK,IAAI,MAAM,OAAO,GAAG;YACrG;YAEA,IAAI,UAAU,cAAc,GAAG;gBAC7B,MAAM,aAAa,2BAA2B,KAAK,GAAG,CAAC,GAAG;gBAC1D,QAAQ,GAAG,CAAC,AAAC,QAAkB,OAAX,YAAW;gBAC/B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;IACF;IAEA,OAAO;QACL,eAAe;QACf,SAAS;QACT,OAAO,AAAC,KAA0B,OAAtB,aAAY,YAAoB,OAAV;QAClC,gBAAgB,KAAK,GAAG,KAAK;QAC7B,eAAe;QACf,YAAY;IACd;AACF;AAGO,eAAe,gBACpB,QAAmE,EACnE,KAAa,EACb,UAA8E,EAC9E,iBAA+D;QAC/D,cAAA,iEAAsB,GACtB,QAAA,iEAAgB,yBAChB,wBAAA,gDAAsC,WAAW;sBAAhB;IAEjC,MAAM,UAAuF,IAAI,MAAM,SAAS,MAAM;IACtH,IAAI,YAAY;IAChB,IAAI,kBAAkB;IACtB,MAAM,YAAY,KAAK,GAAG;IAE1B,aAAa;IACb,MAAM,YAAY,IAAI,UAAU;IAEhC,MAAM,iBAAiB,OAAO,SAA6D;QACzF,MAAM,UAAU,OAAO;QACvB,MAAM,mBAAmB,KAAK,GAAG;QAEjC,IAAI;YAEF,MAAM,SAAS,MAAM,YAAY;gBAC/B,cAAc,QAAQ,OAAO;gBAC7B;gBACA,cAAc,QAAQ,KAAK;gBAC3B,eAAe,QAAQ,MAAM;gBAC7B;YACF;YAEA,MAAM,wBAAwB,KAAK,GAAG,KAAK;YAE3C,IAAI,OAAO,UAAU,EAAE;gBACrB,mBAAmB,OAAO,UAAU;YACtC;YAEA,MAAM,gBAAgB;gBACpB,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,aAAa;gBAC7B,OAAO,OAAO,KAAK;gBACnB,SAAS;oBACP,YAAY,OAAO,UAAU;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,OAAO,OAAO,KAAK;oBACnB,gBAAgB;oBAChB,eAAe,QAAQ,MAAM;oBAC7B,cAAc,QAAQ,KAAK;gBAC7B;YACF;YAEA,OAAO,CAAC,MAAM,GAAG;YACjB;YAEA,WAAW;YACX,IAAI,mBAAmB;gBACrB,kBAAkB,OAAO;YAC3B;YAEA,cAAc;YACd,IAAI,YAAY;gBACd,MAAM,kBAAkB;oBACtB;oBACA,OAAO,SAAS,MAAM;oBACtB;oBACA,WAAW,KAAK,GAAG,KAAK;oBACxB,uBAAuB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;oBAClD,aAAa,WAAW,QAAQ;oBAChC,gBAAgB;wBACd,QAAQ,QAAQ,MAAM;wBACtB,OAAO,QAAQ,KAAK;wBACpB,gBAAgB;wBAChB,QAAQ,OAAO,UAAU;wBACzB,QAAQ,OAAO,UAAU;oBAC3B;gBACF;gBAEA,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM,EAAE;YAClE;YAEA,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,mBAAmB,KAAK,GAAG;YACjC,MAAM,cAAc;gBAClB,SAAS;gBACT,SAAS;gBACT,OAAO,AAAC,SAAwD,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,SAAS;oBACP,eAAe,QAAQ,MAAM;oBAC7B,cAAc,QAAQ,KAAK;oBAC3B,gBAAgB,mBAAmB;gBACrC;YACF;YAEA,OAAO,CAAC,MAAM,GAAG;YACjB;YAEA,IAAI,mBAAmB;gBACrB,kBAAkB,OAAO;YAC3B;YAEA,IAAI,YAAY;gBACd,MAAM,kBAAkB;oBACtB;oBACA,OAAO,SAAS,MAAM;oBACtB;oBACA,WAAW,KAAK,GAAG,KAAK;oBACxB,uBAAuB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;oBAClD,aAAa,WAAW,QAAQ;oBAChC,gBAAgB;wBACd,QAAQ,QAAQ,MAAM;wBACtB,OAAO,QAAQ,KAAK;wBACpB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAClD;gBACF;gBAEA,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM,EAAE;YAClE;YAEA,OAAO;QACT,SAAU;YACR,UAAU,OAAO;QACnB;IACF;IAEA,WAAW;IACX,MAAM,WAAW,SAAS,GAAG,CAAC,CAAC,SAAS,QAAU,eAAe,SAAS;IAC1E,MAAM,QAAQ,GAAG,CAAC;IAElB,sBAAsB;IACtB,IAAI,uBAAuB;QACzB,MAAM,iBAAiB,QACpB,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;gBAAE;gBAAQ;gBAAO,SAAS,QAAQ,CAAC,MAAM;YAAC,CAAC,GACnE,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,MAAM,CAAC,OAAO;QAEtC,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,QAAQ,GAAG,CAAC,AAAC,QAA6B,OAAtB,eAAe,MAAM,EAAC;YAE1C,gBAAgB;YAChB,MAAM,oBAAoB,IAAI,UAAU,IAAI,YAAY;YAExD,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,eAAgB;gBAC/C,MAAM,kBAAkB,OAAO;gBAE/B,IAAI;oBACF,QAAQ,GAAG,CAAC,AAAC,SAA6B,OAArB,QAAQ,MAAM,EAAC,QAAoB,OAAd,QAAQ,KAAK;oBAEvD,YAAY;oBACZ,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBAEjD,MAAM,iBAAiB,MAAM,YAAY;wBACvC,cAAc,QAAQ,OAAO;wBAC7B;wBACA,cAAc,QAAQ,KAAK;wBAC3B,eAAe,QAAQ,MAAM;wBAC7B;oBACF;oBAEA,IAAI,eAAe,OAAO,EAAE;wBAC1B,QAAQ,GAAG,CAAC,AAAC,SAAuB,OAAf,QAAQ,MAAM,EAAC;wBAEpC,MAAM,yBAAyB;4BAC7B,SAAS;4BACT,SAAS,eAAe,aAAa;4BACrC,OAAO;4BACP,SAAS;gCACP,GAAG,cAAc;gCACjB,eAAe,QAAQ,MAAM;gCAC7B,cAAc,QAAQ,KAAK;gCAC3B,aAAa;4BACf;wBACF;wBAEA,OAAO,CAAC,MAAM,GAAG;wBACjB;wBAEA,WAAW;wBACX,IAAI,mBAAmB;4BACrB,kBAAkB,OAAO;wBAC3B;wBAEA,OAAO;wBACP,IAAI,YAAY;4BACd,MAAM,kBAAkB;gCACtB;gCACA,OAAO,SAAS,MAAM;gCACtB,iBAAiB,kBAAkB,CAAC,eAAe,UAAU,IAAI,CAAC;gCAClE,WAAW,KAAK,GAAG,KAAK;gCACxB,uBAAuB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;gCAClD,aAAa,WAAW,QAAQ;gCAChC,gBAAgB;oCACd,QAAQ,QAAQ,MAAM;oCACtB,OAAO,QAAQ,KAAK;oCACpB,gBAAgB,eAAe,cAAc;oCAC7C,QAAQ,eAAe,UAAU;oCACjC,QAAQ,eAAe,UAAU;oCACjC,aAAa;gCACf;4BACF;4BAEA,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM,EAAE;wBAClE;oBACF,OAAO;wBACL,QAAQ,GAAG,CAAC,AAAC,KAA6B,OAAzB,QAAQ,MAAM,EAAC,YAA+B,OAArB,eAAe,KAAK;wBAC9D,qBAAqB;wBACrB,OAAO,CAAC,MAAM,GAAG;4BACf,GAAG,OAAO,CAAC,MAAM;4BACjB,OAAO,AAAC,SAAuC,OAA/B,OAAO,CAAC,MAAM,CAAC,KAAK,EAAC,YAA+B,OAArB,eAAe,KAAK;4BACnE,SAAS;gCACP,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO;gCACzB,mBAAmB;gCACnB,eAAe,eAAe,KAAK;gCACnC,uBAAuB,eAAe,aAAa;4BACrD;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,AAAC,OAAqB,OAAf,QAAQ,MAAM,EAAC,aAAW;oBAC/C,OAAO,CAAC,MAAM,GAAG;wBACf,GAAG,OAAO,CAAC,MAAM;wBACjB,OAAO,AAAC,GAAiC,OAA/B,OAAO,CAAC,MAAM,CAAC,KAAK,EAAC,YAA0D,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClF,SAAS;4BACP,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO;4BACzB,mBAAmB;4BACnB,mBAAmB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAC9D;oBACF;gBACF,SAAU;oBACR,kBAAkB,OAAO;gBAC3B;YACF;QACF;IACF;IAEA,OAAO;AACT;AAEA,cAAc;AACd,MAAM;IAQJ,MAAM,UAAyB;QAC7B,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG;YACpB,IAAI,CAAC,OAAO;YACZ,OAAO,QAAQ,OAAO;QACxB;QAEA,OAAO,IAAI,QAAc,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACtB;IACF;IAEA,UAAgB;QACd,IAAI,CAAC,OAAO;QACZ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG;YAC7B,MAAM,UAAU,IAAI,CAAC,SAAS,CAAC,KAAK;YACpC,IAAI,SAAS;gBACX,IAAI,CAAC,OAAO;gBACZ;YACF;QACF;IACF;IAxBA,YAAY,OAAe,CAAE;QAH7B,+KAAQ,WAAR,KAAA;QACA,+KAAQ,aAA+B,EAAE;QAGvC,IAAI,CAAC,OAAO,GAAG;IACjB;AAuBF;AAGO,eAAe;IAKpB,IAAI;QACF,MAAM,aAAa,MAAM,YAAY;YACnC,cAAc;YACd,OAAO;QACT;QAEA,OAAO;YACL,SAAS,WAAW,OAAO;YAC3B,OAAO,WAAW,KAAK;YACvB,SAAS;gBACP,YAAY,WAAW,UAAU;gBACjC,YAAY,WAAW,UAAU;gBACjC,OAAO,WAAW,KAAK;gBACvB,gBAAgB,WAAW,cAAc;gBACzC,aAAa,WAAW,QAAQ;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,AAAC,WAA0D,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3D,SAAS;gBACP,aAAa,WAAW,QAAQ;YAClC;QACF;IACF;AACF;AAGO,SAAS;IACd,OAAO,WAAW,QAAQ;AAC5B;AAGO,SAAS;IACd,SAAS,OAAO,CAAC,CAAA;QACf,IAAI,YAAY,GAAG;QACnB,IAAI,QAAQ,GAAG;QACf,IAAI,aAAa,GAAG;IACtB;AACF;AAGO,IAAI,eAAqF;IAC9F,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,uBAAuB;QACrB,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;AACF;AAGO,SAAS;IACd,iCAAiC;IACjC,wCAAmC;QACjC,QAAQ,IAAI,CAAC;QACb;IACF;;;AAiBF;AAGO,SAAS,gBAAgB,IAAY,EAAE,WAAmB,EAAE,KAAa;IAC9E,MAAM,MAAM,AAAC,UAAoB,OAAX,KAAK,GAAG;IAC9B,eAAe;QACb,GAAG,YAAY;QACf,CAAC,IAAI,EAAE;YACL;YACA;YACA;QACF;IACF;IACA,OAAO;AACT;AAGO,eAAe,uBACpB,OAAe,EACf,aAAqB,EACrB,YAAoB,EACpB,KAAa,EACb,YAAqB,EACrB,KAAc;IAEd,aAAa;IACb,wCAAmC;QACjC,QAAQ,IAAI,CAAC;QACb,iBAAiB;QACjB,OAAO,MAAM,YAAY;YACvB;YACA;YACA;YACA;YACA;QACF;IACF;;;AA+CF", "debugId": null}}, {"offset": {"line": 1567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/RuleEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Settings, Wand2, Save } from 'lucide-react';\nimport { PRESET_RULES } from '@/lib/gemini';\n\ninterface RuleEditorProps {\n  rules: string;\n  onRulesChange: (rules: string) => void;\n  disabled?: boolean;\n  onSaveToPreset?: (rules: string) => void;\n}\n\ninterface Preset {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n}\n\nexport default function RuleEditor({ rules, onRulesChange, disabled, onSaveToPreset }: RuleEditorProps) {\n  const [showPresets, setShowPresets] = useState(false);\n  const [customPresets, setCustomPresets] = useState<Preset[]>([]);\n  const [allPresets, setAllPresets] = useState<Record<string, { name: string; description: string; rules: string }>>({});\n\n  // 加载自定义预设\n  useEffect(() => {\n    loadCustomPresets();\n  }, []);\n\n  const loadCustomPresets = async () => {\n    try {\n      const response = await fetch('/api/presets');\n      const result = await response.json();\n      if (result.success) {\n        setCustomPresets(result.data);\n\n        // 合并内置预设和自定义预设\n        const combined = { ...PRESET_RULES };\n        result.data.forEach((preset: Preset) => {\n          combined[`custom_${preset.id}`] = {\n            name: preset.name,\n            description: preset.description,\n            rules: preset.rules\n          };\n        });\n        setAllPresets(combined);\n      }\n    } catch (error) {\n      console.error('加载自定义预设失败:', error);\n      setAllPresets(PRESET_RULES);\n    }\n  };\n\n  const handlePresetSelect = (presetKey: string) => {\n    const preset = allPresets[presetKey];\n    if (preset) {\n      onRulesChange(preset.rules);\n      setShowPresets(false);\n    }\n  };\n\n  const handleSaveToPreset = async () => {\n    if (rules.trim() && onSaveToPreset) {\n      await onSaveToPreset(rules);\n      // 重新加载预设列表\n      await loadCustomPresets();\n    }\n  };\n\n  const presetButtons = Object.entries(allPresets).filter(([key]) => key !== 'custom');\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n          <Settings className=\"mr-2\" size={20} />\n          改写规则\n        </h2>\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={handleSaveToPreset}\n            disabled={disabled || !rules.trim()}\n            className=\"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n            title=\"保存为预设\"\n          >\n            <Save size={16} />\n          </button>\n          <button\n            onClick={() => setShowPresets(!showPresets)}\n            disabled={disabled}\n            className=\"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n            title=\"预设规则\"\n          >\n            <Wand2 size={16} />\n          </button>\n        </div>\n      </div>\n\n\n\n      {/* 预设规则 */}\n      {showPresets && (\n        <div className=\"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <h3 className=\"font-medium text-gray-800 mb-2 text-sm\">选择预设规则</h3>\n          <div className=\"grid grid-cols-1 gap-1\">\n            {presetButtons.map(([key, preset]) => (\n              <button\n                key={key}\n                onClick={() => handlePresetSelect(key)}\n                disabled={disabled}\n                className=\"text-left p-2 border border-gray-200 rounded hover:border-blue-300 hover:bg-blue-50 disabled:opacity-50 transition-colors\"\n              >\n                <div className=\"font-medium text-gray-800 text-sm\">{preset.name}</div>\n                <div className=\"text-xs text-gray-600\">{preset.description}</div>\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 规则编辑器 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          改写规则内容\n        </label>\n        <textarea\n          value={rules}\n          onChange={(e) => onRulesChange(e.target.value)}\n          disabled={disabled}\n          placeholder=\"请输入详细的改写规则，例如：&#10;&#10;1. 扩写男女主角之间的互动情节&#10;2. 对战斗场面一笔带过&#10;3. 增加情感描写和心理活动&#10;4. 修改不合理的人物行为&#10;...\"\n          className=\"w-full h-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none disabled:bg-gray-100\"\n        />\n        <div className=\"mt-2 text-xs text-gray-500\">\n          {rules.length} 字符 • 建议详细描述改写要求以获得更好的效果\n        </div>\n      </div>\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAoBe,SAAS,WAAW,KAAmE;QAAnE,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,EAAmB,GAAnE;;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAW,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAuE,CAAC;IAEpH,UAAU;IACV,IAAA,0KAAS;gCAAC;YACR;QACF;+BAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,iBAAiB,OAAO,IAAI;gBAE5B,eAAe;gBACf,MAAM,WAAW;oBAAE,GAAG,uIAAY;gBAAC;gBACnC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;oBACnB,QAAQ,CAAC,AAAC,UAAmB,OAAV,OAAO,EAAE,EAAG,GAAG;wBAChC,MAAM,OAAO,IAAI;wBACjB,aAAa,OAAO,WAAW;wBAC/B,OAAO,OAAO,KAAK;oBACrB;gBACF;gBACA,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,cAAc,uIAAY;QAC5B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAS,UAAU,CAAC,UAAU;QACpC,IAAI,QAAQ;YACV,cAAc,OAAO,KAAK;YAC1B,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,MAAM,IAAI,MAAM,gBAAgB;YAClC,MAAM,eAAe;YACrB,WAAW;YACX,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,OAAO,OAAO,CAAC,YAAY,MAAM,CAAC;YAAC,CAAC,IAAI;eAAK,QAAQ;;IAE3E,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,yNAAQ;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU,YAAY,CAAC,MAAM,IAAI;gCACjC,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,6MAAI;oCAAC,MAAM;;;;;;;;;;;0CAEd,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,UAAU;gCACV,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,2NAAK;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;YAQlB,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC;gCAAC,CAAC,KAAK,OAAO;iDAC/B,6LAAC;gCAEC,SAAS,IAAM,mBAAmB;gCAClC,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDAAqC,OAAO,IAAI;;;;;;kDAC/D,6LAAC;wCAAI,WAAU;kDAAyB,OAAO,WAAW;;;;;;;+BANrD;;;;;;;;;;;;;;;;;0BAcf,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,UAAU;wBACV,aAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;AAOxB;GAzHwB;KAAA", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/CharacterManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Users, Plus, X } from 'lucide-react';\n\ninterface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string;\n  description: string;\n  personality?: string;\n  appearance?: string;\n  relationships?: string;\n}\n\ninterface CharacterManagerProps {\n  novelId?: string;\n  characters: Character[];\n  onCharactersChange: (characters: Character[]) => void;\n  disabled?: boolean;\n}\n\nexport default function CharacterManager({ novelId, characters, onCharactersChange, disabled }: CharacterManagerProps) {\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [newCharacter, setNewCharacter] = useState({\n    name: '',\n    role: '其他',\n    description: ''\n  });\n\n  // 当小说ID变化时，加载对应的人物设定\n  useEffect(() => {\n    if (novelId) {\n      loadCharacters();\n    } else {\n      onCharactersChange([]);\n    }\n  }, [novelId]);\n\n  const loadCharacters = async () => {\n    if (!novelId) return;\n\n    try {\n      const response = await fetch(`/api/characters?novelId=${novelId}`);\n      const result = await response.json();\n      if (result.success) {\n        onCharactersChange(result.data);\n      }\n    } catch (error) {\n      console.error('加载人物设定失败:', error);\n    }\n  };\n\n  const handleAddCharacter = async () => {\n    if (!newCharacter.name.trim() || !novelId) return;\n\n    setLoading(true);\n    try {\n      const response = await fetch('/api/characters', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          novelId,\n          name: newCharacter.name,\n          role: newCharacter.role,\n          description: newCharacter.description,\n        }),\n      });\n\n      const result = await response.json();\n      if (result.success) {\n        await loadCharacters(); // 重新加载列表\n        setNewCharacter({ name: '', role: '其他', description: '' });\n        setShowAddForm(false);\n      } else {\n        alert(`添加失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('添加人物失败:', error);\n      alert('添加人物失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRemoveCharacter = async (id: string) => {\n    if (!confirm('确定要删除这个人物设定吗？')) return;\n\n    try {\n      const response = await fetch(`/api/characters?id=${id}`, {\n        method: 'DELETE',\n      });\n\n      const result = await response.json();\n      if (result.success) {\n        await loadCharacters(); // 重新加载列表\n      } else {\n        alert(`删除失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('删除人物失败:', error);\n      alert('删除人物失败');\n    }\n  };\n\n  const characterTypes = ['男主', '女主', '配角', '反派', '其他'];\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h2 className=\"text-lg font-semibold text-gray-800 flex items-center\">\n          <Users className=\"mr-2\" size={18} />\n          人物设定\n        </h2>\n        <button\n          onClick={() => setShowAddForm(!showAddForm)}\n          disabled={disabled}\n          className=\"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n          title=\"添加人物\"\n        >\n          <Plus size={16} />\n        </button>\n      </div>\n\n      {/* 添加人物表单 */}\n      {showAddForm && (\n        <div className=\"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <div className=\"space-y-2\">\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                placeholder=\"人物名称\"\n                value={newCharacter.name}\n                onChange={(e) => setNewCharacter({ ...newCharacter, name: e.target.value })}\n                className=\"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n              />\n              <select\n                value={newCharacter.role}\n                onChange={(e) => setNewCharacter({ ...newCharacter, role: e.target.value })}\n                className=\"px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n              >\n                {characterTypes.map(type => (\n                  <option key={type} value={type}>{type}</option>\n                ))}\n              </select>\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"备注描述\"\n              value={newCharacter.description}\n              onChange={(e) => setNewCharacter({ ...newCharacter, description: e.target.value })}\n              className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n            />\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={handleAddCharacter}\n                className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                添加\n              </button>\n              <button\n                onClick={() => setShowAddForm(false)}\n                className=\"px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600\"\n              >\n                取消\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 人物列表 */}\n      <div className=\"space-y-2\">\n        {characters.length === 0 ? (\n          <div className=\"text-center py-4 text-gray-500 text-sm\">\n            暂无人物设定\n          </div>\n        ) : (\n          characters.map((character) => (\n            <div key={character.id} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"font-medium text-gray-800 text-sm\">{character.name}</span>\n                  <span className={`px-2 py-0.5 text-xs rounded ${\n                    character.role === '男主' ? 'bg-blue-100 text-blue-800' :\n                    character.role === '女主' ? 'bg-pink-100 text-pink-800' :\n                    character.role === '配角' ? 'bg-green-100 text-green-800' :\n                    character.role === '反派' ? 'bg-red-100 text-red-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {character.role}\n                  </span>\n                </div>\n                {character.description && (\n                  <div className=\"text-xs text-gray-600 mt-1 truncate\">\n                    {character.description}\n                  </div>\n                )}\n              </div>\n              <button\n                onClick={() => handleRemoveCharacter(character.id)}\n                disabled={disabled}\n                className=\"p-1 text-gray-400 hover:text-red-600 disabled:opacity-50\"\n                title=\"删除\"\n              >\n                <X size={14} />\n              </button>\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAuBe,SAAS,iBAAiB,KAA4E;QAA5E,EAAE,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE,QAAQ,EAAyB,GAA5E;;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;QAC/C,MAAM;QACN,MAAM;QACN,aAAa;IACf;IAEA,qBAAqB;IACrB,IAAA,0KAAS;sCAAC;YACR,IAAI,SAAS;gBACX;YACF,OAAO;gBACL,mBAAmB,EAAE;YACvB;QACF;qCAAG;QAAC;KAAQ;IAEZ,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,2BAAkC,OAAR;YACxD,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,mBAAmB,OAAO,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS;QAE3C,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,MAAM,aAAa,IAAI;oBACvB,MAAM,aAAa,IAAI;oBACvB,aAAa,aAAa,WAAW;gBACvC;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,kBAAkB,SAAS;gBACjC,gBAAgB;oBAAE,MAAM;oBAAI,MAAM;oBAAM,aAAa;gBAAG;gBACxD,eAAe;YACjB,OAAO;gBACL,MAAM,AAAC,SAAqB,OAAb,OAAO,KAAK;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI,CAAC,QAAQ,kBAAkB;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,sBAAwB,OAAH,KAAM;gBACvD,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,kBAAkB,SAAS;YACnC,OAAO;gBACL,MAAM,AAAC,SAAqB,OAAb,OAAO,KAAK;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IAErD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,gNAAK;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGtC,6LAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,UAAU;wBACV,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,6MAAI;4BAAC,MAAM;;;;;;;;;;;;;;;;;YAKf,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,aAAa,IAAI;oCACxB,UAAU,CAAC,IAAM,gBAAgB;4CAAE,GAAG,YAAY;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACzE,WAAU;;;;;;8CAEZ,6LAAC;oCACC,OAAO,aAAa,IAAI;oCACxB,UAAU,CAAC,IAAM,gBAAgB;4CAAE,GAAG,YAAY;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACzE,WAAU;8CAET,eAAe,GAAG,CAAC,CAAA,qBAClB,6LAAC;4CAAkB,OAAO;sDAAO;2CAApB;;;;;;;;;;;;;;;;sCAInB,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO,aAAa,WAAW;4BAC/B,UAAU,CAAC,IAAM,gBAAgB;oCAAE,GAAG,YAAY;oCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAChF,WAAU;;;;;;sCAEZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;0BACZ,WAAW,MAAM,KAAK,kBACrB,6LAAC;oBAAI,WAAU;8BAAyC;;;;;2BAIxD,WAAW,GAAG,CAAC,CAAC,0BACd,6LAAC;wBAAuB,WAAU;;0CAChC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAqC,UAAU,IAAI;;;;;;0DACnE,6LAAC;gDAAK,WAAW,AAAC,+BAMjB,OALC,UAAU,IAAI,KAAK,OAAO,8BAC1B,UAAU,IAAI,KAAK,OAAO,8BAC1B,UAAU,IAAI,KAAK,OAAO,gCAC1B,UAAU,IAAI,KAAK,OAAO,4BAC1B;0DAEC,UAAU,IAAI;;;;;;;;;;;;oCAGlB,UAAU,WAAW,kBACpB,6LAAC;wCAAI,WAAU;kDACZ,UAAU,WAAW;;;;;;;;;;;;0CAI5B,6LAAC;gCACC,SAAS,IAAM,sBAAsB,UAAU,EAAE;gCACjD,UAAU;gCACV,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,oMAAC;oCAAC,MAAM;;;;;;;;;;;;uBA1BH,UAAU,EAAE;;;;;;;;;;;;;;;;AAkClC;GAlMwB;KAAA", "debugId": null}}, {"offset": {"line": 2174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/FailedChaptersRetry.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\n\ninterface FailedChapter {\n  chapterNumber: number;\n  chapterTitle: string;\n  error?: string;\n  apiKeyUsed?: string;\n  processingTime?: number;\n  detailedError?: string;\n  debugInfo?: any; // 新增调试信息\n}\n\ninterface FailedChaptersRetryProps {\n  jobId: string;\n  failedChapters: FailedChapter[];\n  rules: string;\n  model?: string;\n  onRetryStart?: () => void;\n  onRetryComplete?: (success: boolean, message: string) => void;\n}\n\nexport default function FailedChaptersRetry({\n  jobId,\n  failedChapters,\n  rules,\n  model = 'gemini-2.5-flash-lite',\n  onRetryStart,\n  onRetryComplete,\n}: FailedChaptersRetryProps) {\n  const [selectedChapters, setSelectedChapters] = useState<number[]>([]);\n  const [isRetrying, setIsRetrying] = useState(false);\n  const [retryMessage, setRetryMessage] = useState('');\n\n  const handleSelectAll = () => {\n    if (selectedChapters.length === failedChapters.length) {\n      setSelectedChapters([]);\n    } else {\n      setSelectedChapters(failedChapters.map(ch => ch.chapterNumber));\n    }\n  };\n\n  const handleChapterToggle = (chapterNumber: number) => {\n    setSelectedChapters(prev => \n      prev.includes(chapterNumber)\n        ? prev.filter(num => num !== chapterNumber)\n        : [...prev, chapterNumber]\n    );\n  };\n\n  const handleRetry = async () => {\n    if (selectedChapters.length === 0) {\n      alert('请选择要重试的章节');\n      return;\n    }\n\n    setIsRetrying(true);\n    setRetryMessage('正在重试失败的章节...');\n    \n    if (onRetryStart) {\n      onRetryStart();\n    }\n\n    try {\n      const response = await fetch('/api/rewrite/retry', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          jobId,\n          chapterNumbers: selectedChapters,\n          rules,\n          model,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setRetryMessage(`重试任务已创建，正在处理 ${selectedChapters.length} 个章节...`);\n        if (onRetryComplete) {\n          onRetryComplete(true, data.data.message);\n        }\n      } else {\n        setRetryMessage(`重试失败: ${data.error}`);\n        if (onRetryComplete) {\n          onRetryComplete(false, data.error);\n        }\n      }\n    } catch (error) {\n      const errorMessage = `重试请求失败: ${error instanceof Error ? error.message : '未知错误'}`;\n      setRetryMessage(errorMessage);\n      if (onRetryComplete) {\n        onRetryComplete(false, errorMessage);\n      }\n    } finally {\n      setIsRetrying(false);\n    }\n  };\n\n  if (failedChapters.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mt-4\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-red-800\">\n          失败章节 ({failedChapters.length} 个)\n        </h3>\n        <div className=\"flex gap-2\">\n          <button\n            onClick={handleSelectAll}\n            className=\"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded transition-colors\"\n          >\n            {selectedChapters.length === failedChapters.length ? '取消全选' : '全选'}\n          </button>\n          <button\n            onClick={handleRetry}\n            disabled={isRetrying || selectedChapters.length === 0}\n            className=\"px-4 py-1 text-sm bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded transition-colors\"\n          >\n            {isRetrying ? '重试中...' : `重试选中章节 (${selectedChapters.length})`}\n          </button>\n        </div>\n      </div>\n\n      {retryMessage && (\n        <div className=\"mb-4 p-3 bg-blue-50 border border-blue-200 rounded text-blue-800\">\n          {retryMessage}\n        </div>\n      )}\n\n      <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n        {failedChapters.map((chapter) => (\n          <div\n            key={chapter.chapterNumber}\n            className=\"flex items-start gap-3 p-3 bg-white border border-red-200 rounded\"\n          >\n            <input\n              type=\"checkbox\"\n              checked={selectedChapters.includes(chapter.chapterNumber)}\n              onChange={() => handleChapterToggle(chapter.chapterNumber)}\n              className=\"mt-1\"\n            />\n            <div className=\"flex-1 min-w-0\">\n              <div className=\"flex items-center gap-2 mb-1\">\n                <span className=\"font-medium text-gray-900\">\n                  第 {chapter.chapterNumber} 章\n                </span>\n                <span className=\"text-gray-600 truncate\">\n                  {chapter.chapterTitle}\n                </span>\n              </div>\n              \n              <div className=\"text-sm text-red-600 mb-1\">\n                错误: {chapter.error || '未知错误'}\n              </div>\n              \n              {chapter.detailedError && (\n                <details className=\"text-xs text-gray-500\">\n                  <summary className=\"cursor-pointer hover:text-gray-700\">\n                    详细错误信息\n                  </summary>\n                  <div className=\"mt-1 p-2 bg-gray-50 rounded border text-xs font-mono whitespace-pre-wrap\">\n                    {chapter.detailedError}\n                  </div>\n                </details>\n              )}\n\n              {chapter.debugInfo && (\n                <details className=\"text-xs text-gray-500 mt-2\">\n                  <summary className=\"cursor-pointer hover:text-gray-700 text-blue-600\">\n                    🔍 调试信息 (发送给模型的内容)\n                  </summary>\n                  <div className=\"mt-2 space-y-2\">\n                    {/* 章节信息 */}\n                    <div className=\"p-2 bg-blue-50 rounded border\">\n                      <div className=\"font-semibold text-blue-800 mb-1\">章节信息:</div>\n                      <div className=\"font-mono text-xs\">\n                        <div>章节号: {chapter.debugInfo.chapterInfo?.number}</div>\n                        <div>标题: {chapter.debugInfo.chapterInfo?.title}</div>\n                        <div>内容长度: {chapter.debugInfo.chapterInfo?.contentLength} 字符</div>\n                        <div>使用模型: {chapter.debugInfo.chapterInfo?.model}</div>\n                      </div>\n                    </div>\n\n                    {/* 请求信息 */}\n                    <div className=\"p-2 bg-green-50 rounded border\">\n                      <div className=\"font-semibold text-green-800 mb-1\">请求信息:</div>\n                      <div className=\"font-mono text-xs\">\n                        <div>提示词长度: {chapter.debugInfo.requestInfo?.promptLength} 字符</div>\n                        <div>API URL: {chapter.debugInfo.requestInfo?.apiUrl}</div>\n                        <div>API Key: {chapter.debugInfo.requestInfo?.apiKeyName}</div>\n                      </div>\n                    </div>\n\n                    {/* API响应信息 */}\n                    <div className=\"p-2 bg-red-50 rounded border\">\n                      <div className=\"font-semibold text-red-800 mb-1\">API响应信息:</div>\n                      <div className=\"font-mono text-xs\">\n                        <div>HTTP状态: {chapter.debugInfo.responseInfo?.status} {chapter.debugInfo.responseInfo?.statusText}</div>\n                        <div>有数据: {chapter.debugInfo.responseInfo?.hasData ? '是' : '否'}</div>\n                        <div>数据字段: {chapter.debugInfo.responseInfo?.dataKeys?.join(', ')}</div>\n                        <div>candidates: {chapter.debugInfo.responseInfo?.candidates ? JSON.stringify(chapter.debugInfo.responseInfo.candidates) : '无'}</div>\n                        <div>candidates长度: {chapter.debugInfo.responseInfo?.candidatesLength || 0}</div>\n                      </div>\n                    </div>\n\n                    {/* 完整API响应 */}\n                    <details className=\"p-2 bg-gray-50 rounded border\">\n                      <summary className=\"cursor-pointer font-semibold text-gray-800\">\n                        完整API响应 (点击展开)\n                      </summary>\n                      <pre className=\"mt-2 text-xs overflow-x-auto whitespace-pre-wrap break-words\">\n                        {chapter.debugInfo.responseInfo?.fullResponse}\n                      </pre>\n                    </details>\n                  </div>\n                </details>\n              )}\n              \n              <div className=\"flex gap-4 text-xs text-gray-500 mt-1\">\n                {chapter.apiKeyUsed && (\n                  <span>API Key: {chapter.apiKeyUsed}</span>\n                )}\n                {chapter.processingTime && (\n                  <span>处理时间: {chapter.processingTime}ms</span>\n                )}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded\">\n        <h4 className=\"font-medium text-yellow-800 mb-2\">重试建议:</h4>\n        <ul className=\"text-sm text-yellow-700 space-y-1\">\n          <li>• 重试将使用更保守的策略，串行处理章节以避免API限制</li>\n          <li>• 如果仍然失败，可能需要检查API key配额或调整改写规则</li>\n          <li>• 建议在API使用量较低的时间段进行重试</li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAuBe,SAAS,oBAAoB,KAOjB;QAPiB,EAC1C,KAAK,EACL,cAAc,EACd,KAAK,EACL,QAAQ,uBAAuB,EAC/B,YAAY,EACZ,eAAe,EACU,GAPiB;;IAQ1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAW,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IAEjD,MAAM,kBAAkB;QACtB,IAAI,iBAAiB,MAAM,KAAK,eAAe,MAAM,EAAE;YACrD,oBAAoB,EAAE;QACxB,OAAO;YACL,oBAAoB,eAAe,GAAG,CAAC,CAAA,KAAM,GAAG,aAAa;QAC/D;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB,CAAA,OAClB,KAAK,QAAQ,CAAC,iBACV,KAAK,MAAM,CAAC,CAAA,MAAO,QAAQ,iBAC3B;mBAAI;gBAAM;aAAc;IAEhC;IAEA,MAAM,cAAc;QAClB,IAAI,iBAAiB,MAAM,KAAK,GAAG;YACjC,MAAM;YACN;QACF;QAEA,cAAc;QACd,gBAAgB;QAEhB,IAAI,cAAc;YAChB;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,gBAAgB;oBAChB;oBACA;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,AAAC,gBAAuC,OAAxB,iBAAiB,MAAM,EAAC;gBACxD,IAAI,iBAAiB;oBACnB,gBAAgB,MAAM,KAAK,IAAI,CAAC,OAAO;gBACzC;YACF,OAAO;gBACL,gBAAgB,AAAC,SAAmB,OAAX,KAAK,KAAK;gBACnC,IAAI,iBAAiB;oBACnB,gBAAgB,OAAO,KAAK,KAAK;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,AAAC,WAA0D,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzE,gBAAgB;YAChB,IAAI,iBAAiB;gBACnB,gBAAgB,OAAO;YACzB;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAqC;4BAC1C,eAAe,MAAM;4BAAC;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAET,iBAAiB,MAAM,KAAK,eAAe,MAAM,GAAG,SAAS;;;;;;0CAEhE,6LAAC;gCACC,SAAS;gCACT,UAAU,cAAc,iBAAiB,MAAM,KAAK;gCACpD,WAAU;0CAET,aAAa,WAAW,AAAC,WAAkC,OAAxB,iBAAiB,MAAM,EAAC;;;;;;;;;;;;;;;;;;YAKjE,8BACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,6LAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC;wBA8CM,gCACD,iCACE,iCACA,iCAQC,gCACE,iCACA,iCAQD,iCAAyC,kCAC5C,kCACC,0CAAA,kCACM,kCACE,kCAUnB;yCAhFf,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCACC,MAAK;gCACL,SAAS,iBAAiB,QAAQ,CAAC,QAAQ,aAAa;gCACxD,UAAU,IAAM,oBAAoB,QAAQ,aAAa;gCACzD,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;oDAA4B;oDACvC,QAAQ,aAAa;oDAAC;;;;;;;0DAE3B,6LAAC;gDAAK,WAAU;0DACb,QAAQ,YAAY;;;;;;;;;;;;kDAIzB,6LAAC;wCAAI,WAAU;;4CAA4B;4CACpC,QAAQ,KAAK,IAAI;;;;;;;oCAGvB,QAAQ,aAAa,kBACpB,6LAAC;wCAAQ,WAAU;;0DACjB,6LAAC;gDAAQ,WAAU;0DAAqC;;;;;;0DAGxD,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,aAAa;;;;;;;;;;;;oCAK3B,QAAQ,SAAS,kBAChB,6LAAC;wCAAQ,WAAU;;0DACjB,6LAAC;gDAAQ,WAAU;0DAAmD;;;;;;0DAGtE,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAmC;;;;;;0EAClD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAI;6EAAM,iCAAA,QAAQ,SAAS,CAAC,WAAW,cAA7B,qDAAA,+BAA+B,MAAM;;;;;;;kFAChD,6LAAC;;4EAAI;6EAAK,kCAAA,QAAQ,SAAS,CAAC,WAAW,cAA7B,sDAAA,gCAA+B,KAAK;;;;;;;kFAC9C,6LAAC;;4EAAI;6EAAO,kCAAA,QAAQ,SAAS,CAAC,WAAW,cAA7B,sDAAA,gCAA+B,aAAa;4EAAC;;;;;;;kFACzD,6LAAC;;4EAAI;6EAAO,kCAAA,QAAQ,SAAS,CAAC,WAAW,cAA7B,sDAAA,gCAA+B,KAAK;;;;;;;;;;;;;;;;;;;kEAKpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAoC;;;;;;0EACnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAI;6EAAQ,iCAAA,QAAQ,SAAS,CAAC,WAAW,cAA7B,qDAAA,+BAA+B,YAAY;4EAAC;;;;;;;kFACzD,6LAAC;;4EAAI;6EAAU,kCAAA,QAAQ,SAAS,CAAC,WAAW,cAA7B,sDAAA,gCAA+B,MAAM;;;;;;;kFACpD,6LAAC;;4EAAI;6EAAU,kCAAA,QAAQ,SAAS,CAAC,WAAW,cAA7B,sDAAA,gCAA+B,UAAU;;;;;;;;;;;;;;;;;;;kEAK5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAkC;;;;;;0EACjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAI;6EAAS,kCAAA,QAAQ,SAAS,CAAC,YAAY,cAA9B,sDAAA,gCAAgC,MAAM;4EAAC;6EAAE,mCAAA,QAAQ,SAAS,CAAC,YAAY,cAA9B,uDAAA,iCAAgC,UAAU;;;;;;;kFACjG,6LAAC;;4EAAI;4EAAM,EAAA,mCAAA,QAAQ,SAAS,CAAC,YAAY,cAA9B,uDAAA,iCAAgC,OAAO,IAAG,MAAM;;;;;;;kFAC3D,6LAAC;;4EAAI;6EAAO,mCAAA,QAAQ,SAAS,CAAC,YAAY,cAA9B,wDAAA,2CAAA,iCAAgC,QAAQ,cAAxC,+DAAA,yCAA0C,IAAI,CAAC;;;;;;;kFAC3D,6LAAC;;4EAAI;4EAAa,EAAA,mCAAA,QAAQ,SAAS,CAAC,YAAY,cAA9B,uDAAA,iCAAgC,UAAU,IAAG,KAAK,SAAS,CAAC,QAAQ,SAAS,CAAC,YAAY,CAAC,UAAU,IAAI;;;;;;;kFAC3H,6LAAC;;4EAAI;4EAAe,EAAA,mCAAA,QAAQ,SAAS,CAAC,YAAY,cAA9B,uDAAA,iCAAgC,gBAAgB,KAAI;;;;;;;;;;;;;;;;;;;kEAK5E,6LAAC;wDAAQ,WAAU;;0EACjB,6LAAC;gEAAQ,WAAU;0EAA6C;;;;;;0EAGhE,6LAAC;gEAAI,WAAU;2EACZ,mCAAA,QAAQ,SAAS,CAAC,YAAY,cAA9B,uDAAA,iCAAgC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;kDAOvD,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,UAAU,kBACjB,6LAAC;;oDAAK;oDAAU,QAAQ,UAAU;;;;;;;4CAEnC,QAAQ,cAAc,kBACrB,6LAAC;;oDAAK;oDAAO,QAAQ,cAAc;oDAAC;;;;;;;;;;;;;;;;;;;;uBA3FrC,QAAQ,aAAa;;;;;;;;;;;0BAmGhC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd;GAhOwB;KAAA", "debugId": null}}, {"offset": {"line": 2762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/DiagnosticsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>ert<PERSON>riangle, CheckCircle, XCircle, Info, RefreshCw } from 'lucide-react';\n\ninterface DiagnosticsData {\n  jobInfo: {\n    id: string;\n    status: string;\n    progress: number;\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    model?: string;\n    concurrency?: number;\n  };\n  apiKeyStatus: {\n    stats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n      cooldownRemaining?: number;\n    }>;\n    connectionTest: {\n      success: boolean;\n      error?: string;\n      details?: any;\n    };\n    recommendations: Array<{\n      type: string;\n      message: string;\n      action: string;\n    }>;\n  };\n  errorAnalysis: {\n    totalFailures: number;\n    errorTypes: Record<string, number>;\n    apiKeyErrors: Record<string, number>;\n    patterns: {\n      timeoutErrors: number;\n      contentErrors: number;\n      networkErrors: number;\n    };\n    mostCommonError: string;\n    problematicApiKey: string;\n  };\n  recommendations: Array<{\n    type: string;\n    category: string;\n    message: string;\n    actions: string[];\n  }>;\n  systemHealth: {\n    timestamp: string;\n    totalApiCalls: number;\n    availableKeys: number;\n    totalKeys: number;\n  };\n}\n\ninterface DiagnosticsPanelProps {\n  jobId: string;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function DiagnosticsPanel({ jobId, isOpen, onClose }: DiagnosticsPanelProps) {\n  const [diagnostics, setDiagnostics] = useState<DiagnosticsData | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchDiagnostics = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch(`/api/rewrite/diagnostics?jobId=${jobId}`);\n      const data = await response.json();\n      \n      if (data.success) {\n        setDiagnostics(data.data);\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError(`获取诊断信息失败: ${err instanceof Error ? err.message : '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    if (isOpen && jobId) {\n      fetchDiagnostics();\n    }\n  }, [isOpen, jobId]);\n\n  const getRecommendationIcon = (type: string) => {\n    switch (type) {\n      case 'error': return <XCircle className=\"w-4 h-4 text-red-500\" />;\n      case 'warning': return <AlertTriangle className=\"w-4 h-4 text-yellow-500\" />;\n      case 'info': return <Info className=\"w-4 h-4 text-blue-500\" />;\n      default: return <CheckCircle className=\"w-4 h-4 text-green-500\" />;\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h2 className=\"text-xl font-semibold\">任务诊断报告</h2>\n          <div className=\"flex gap-2\">\n            <button\n              onClick={fetchDiagnostics}\n              disabled={loading}\n              className=\"p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50\"\n            >\n              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />\n            </button>\n            <button\n              onClick={onClose}\n              className=\"p-2 text-gray-500 hover:text-gray-700\"\n            >\n              ✕\n            </button>\n          </div>\n        </div>\n\n        <div className=\"p-4 overflow-y-auto max-h-[calc(90vh-80px)]\">\n          {loading && (\n            <div className=\"flex items-center justify-center py-8\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n              <span className=\"ml-2\">正在获取诊断信息...</span>\n            </div>\n          )}\n\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-4\">\n              <div className=\"flex items-center text-red-700\">\n                <XCircle className=\"w-5 h-5 mr-2\" />\n                <span>{error}</span>\n              </div>\n            </div>\n          )}\n\n          {diagnostics && (\n            <div className=\"space-y-6\">\n              {/* 任务概览 */}\n              <div className=\"bg-gray-50 rounded-lg p-4\">\n                <h3 className=\"font-semibold mb-3\">任务概览</h3>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-gray-600\">状态:</span>\n                    <span className=\"ml-2 font-medium\">{diagnostics.jobInfo.status}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-600\">进度:</span>\n                    <span className=\"ml-2 font-medium\">{diagnostics.jobInfo.progress}%</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-600\">成功:</span>\n                    <span className=\"ml-2 font-medium text-green-600\">\n                      {diagnostics.jobInfo.completedChapters}/{diagnostics.jobInfo.totalChapters}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-600\">失败:</span>\n                    <span className=\"ml-2 font-medium text-red-600\">\n                      {diagnostics.jobInfo.failedChapters}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* API Key状态 */}\n              <div className=\"bg-blue-50 rounded-lg p-4\">\n                <h3 className=\"font-semibold mb-3\">API Key状态</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <h4 className=\"font-medium mb-2\">连接测试</h4>\n                    <div className={`flex items-center ${\n                      diagnostics.apiKeyStatus.connectionTest.success ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {diagnostics.apiKeyStatus.connectionTest.success ? (\n                        <CheckCircle className=\"w-4 h-4 mr-2\" />\n                      ) : (\n                        <XCircle className=\"w-4 h-4 mr-2\" />\n                      )}\n                      <span className=\"text-sm\">\n                        {diagnostics.apiKeyStatus.connectionTest.success ? '连接正常' : '连接失败'}\n                      </span>\n                    </div>\n                  </div>\n                  <div>\n                    <h4 className=\"font-medium mb-2\">Key统计</h4>\n                    <div className=\"text-sm text-gray-600\">\n                      可用: {diagnostics.systemHealth.availableKeys}/{diagnostics.systemHealth.totalKeys} |\n                      总调用: {diagnostics.systemHealth.totalApiCalls}\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"mt-3\">\n                  <h4 className=\"font-medium mb-2\">详细状态</h4>\n                  <div className=\"space-y-1 text-sm\">\n                    {diagnostics.apiKeyStatus.stats.map((key, index) => (\n                      <div key={index} className=\"flex justify-between items-center\">\n                        <span>{key.name}</span>\n                        <div className=\"flex items-center gap-2\">\n                          <span className=\"text-gray-600\">{key.requestCount} 次调用</span>\n                          <span className={`px-2 py-1 rounded text-xs ${\n                            key.isAvailable ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'\n                          }`}>\n                            {key.isAvailable ? '可用' : '冷却中'}\n                          </span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* 错误分析 */}\n              {diagnostics.errorAnalysis.totalFailures > 0 && (\n                <div className=\"bg-red-50 rounded-lg p-4\">\n                  <h3 className=\"font-semibold mb-3\">错误分析</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <h4 className=\"font-medium mb-2\">错误类型分布</h4>\n                      <div className=\"space-y-1 text-sm\">\n                        {Object.entries(diagnostics.errorAnalysis.errorTypes).map(([type, count]) => (\n                          <div key={type} className=\"flex justify-between\">\n                            <span>{type}</span>\n                            <span className=\"font-medium\">{count}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium mb-2\">关键信息</h4>\n                      <div className=\"space-y-1 text-sm\">\n                        <div>最常见错误: <span className=\"font-medium\">{diagnostics.errorAnalysis.mostCommonError}</span></div>\n                        <div>问题API Key: <span className=\"font-medium\">{diagnostics.errorAnalysis.problematicApiKey}</span></div>\n                        <div>超时错误: <span className=\"font-medium\">{diagnostics.errorAnalysis.patterns.timeoutErrors}</span></div>\n                        <div>内容错误: <span className=\"font-medium\">{diagnostics.errorAnalysis.patterns.contentErrors}</span></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* 建议 */}\n              <div className=\"bg-yellow-50 rounded-lg p-4\">\n                <h3 className=\"font-semibold mb-3\">改进建议</h3>\n                <div className=\"space-y-3\">\n                  {diagnostics.recommendations.map((rec, index) => (\n                    <div key={index} className=\"border border-yellow-200 rounded p-3\">\n                      <div className=\"flex items-center mb-2\">\n                        {getRecommendationIcon(rec.type)}\n                        <span className=\"ml-2 font-medium\">{rec.category}</span>\n                      </div>\n                      <p className=\"text-sm text-gray-700 mb-2\">{rec.message}</p>\n                      <ul className=\"text-sm text-gray-600 space-y-1\">\n                        {rec.actions.map((action, actionIndex) => (\n                          <li key={actionIndex} className=\"flex items-start\">\n                            <span className=\"mr-2\">•</span>\n                            <span>{action}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAmEe,SAAS,iBAAiB,KAAiD;QAAjD,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAyB,GAAjD;;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAyB;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAgB;IAElD,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,kCAAuC,OAAN;YAC/D,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,eAAe,KAAK,IAAI;YAC1B,OAAO;gBACL,SAAS,KAAK,KAAK;YACrB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,AAAC,aAAwD,OAA5C,eAAe,QAAQ,IAAI,OAAO,GAAG;QAC7D,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAA,0KAAS;sCAAC;YACR,IAAI,UAAU,OAAO;gBACnB;YACF;QACF;qCAAG;QAAC;QAAQ;KAAM;IAElB,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAS,qBAAO,6LAAC,0NAAO;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAW,qBAAO,6LAAC,4OAAa;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAQ,qBAAO,6LAAC,6MAAI;oBAAC,WAAU;;;;;;YACpC;gBAAS,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;;;;;;QACzC;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwB;;;;;;sCACtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAEV,cAAA,6LAAC,gOAAS;wCAAC,WAAW,AAAC,WAAwC,OAA9B,UAAU,iBAAiB;;;;;;;;;;;8CAE9D,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAML,6LAAC;oBAAI,WAAU;;wBACZ,yBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAO;;;;;;;;;;;;wBAI1B,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0NAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAM;;;;;;;;;;;;;;;;;wBAKZ,6BACC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAoB,YAAY,OAAO,CAAC,MAAM;;;;;;;;;;;;8DAEhE,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAoB,YAAY,OAAO,CAAC,QAAQ;gEAAC;;;;;;;;;;;;;8DAEnE,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEACb,YAAY,OAAO,CAAC,iBAAiB;gEAAC;gEAAE,YAAY,OAAO,CAAC,aAAa;;;;;;;;;;;;;8DAG9E,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEACb,YAAY,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;8CAO3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAW,AAAC,qBAEhB,OADC,YAAY,YAAY,CAAC,cAAc,CAAC,OAAO,GAAG,mBAAmB;;gEAEpE,YAAY,YAAY,CAAC,cAAc,CAAC,OAAO,iBAC9C,6LAAC,6OAAW;oEAAC,WAAU;;;;;yFAEvB,6LAAC,0NAAO;oEAAC,WAAU;;;;;;8EAErB,6LAAC;oEAAK,WAAU;8EACb,YAAY,YAAY,CAAC,cAAc,CAAC,OAAO,GAAG,SAAS;;;;;;;;;;;;;;;;;;8DAIlE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;;gEAAwB;gEAChC,YAAY,YAAY,CAAC,aAAa;gEAAC;gEAAE,YAAY,YAAY,CAAC,SAAS;gEAAC;gEAC3E,YAAY,YAAY,CAAC,aAAa;;;;;;;;;;;;;;;;;;;sDAKlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,6LAAC;oDAAI,WAAU;8DACZ,YAAY,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,sBACxC,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;8EAAM,IAAI,IAAI;;;;;;8EACf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;;gFAAiB,IAAI,YAAY;gFAAC;;;;;;;sFAClD,6LAAC;4EAAK,WAAW,AAAC,6BAEjB,OADC,IAAI,WAAW,GAAG,gCAAgC;sFAEjD,IAAI,WAAW,GAAG,OAAO;;;;;;;;;;;;;2DAPtB;;;;;;;;;;;;;;;;;;;;;;gCAiBjB,YAAY,aAAa,CAAC,aAAa,GAAG,mBACzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;sEACZ,OAAO,OAAO,CAAC,YAAY,aAAa,CAAC,UAAU,EAAE,GAAG,CAAC;oEAAC,CAAC,MAAM,MAAM;qFACtE,6LAAC;oEAAe,WAAU;;sFACxB,6LAAC;sFAAM;;;;;;sFACP,6LAAC;4EAAK,WAAU;sFAAe;;;;;;;mEAFvB;;;;;;;;;;;;;;;;;8DAOhB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEAAI;sFAAO,6LAAC;4EAAK,WAAU;sFAAe,YAAY,aAAa,CAAC,eAAe;;;;;;;;;;;;8EACpF,6LAAC;;wEAAI;sFAAW,6LAAC;4EAAK,WAAU;sFAAe,YAAY,aAAa,CAAC,iBAAiB;;;;;;;;;;;;8EAC1F,6LAAC;;wEAAI;sFAAM,6LAAC;4EAAK,WAAU;sFAAe,YAAY,aAAa,CAAC,QAAQ,CAAC,aAAa;;;;;;;;;;;;8EAC1F,6LAAC;;wEAAI;sFAAM,6LAAC;4EAAK,WAAU;sFAAe,YAAY,aAAa,CAAC,QAAQ,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQpG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;sDACZ,YAAY,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,sBACrC,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;gEACZ,sBAAsB,IAAI,IAAI;8EAC/B,6LAAC;oEAAK,WAAU;8EAAoB,IAAI,QAAQ;;;;;;;;;;;;sEAElD,6LAAC;4DAAE,WAAU;sEAA8B,IAAI,OAAO;;;;;;sEACtD,6LAAC;4DAAG,WAAU;sEACX,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACxB,6LAAC;oEAAqB,WAAU;;sFAC9B,6LAAC;4EAAK,WAAU;sFAAO;;;;;;sFACvB,6LAAC;sFAAM;;;;;;;mEAFA;;;;;;;;;;;mDARL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwB9B;GAxNwB;KAAA", "debugId": null}}, {"offset": {"line": 3558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/RewriteProgress.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { CheckCircle, XCircle, Clock, AlertCircle, Activity } from 'lucide-react';\nimport FailedChaptersRetry from './FailedChaptersRetry';\nimport DiagnosticsPanel from './DiagnosticsPanel';\n\ninterface RewriteProgressProps {\n  jobId: string;\n  onComplete: () => void;\n}\n\ninterface JobStatus {\n  id: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    averageTimePerChapter: number;\n    apiKeyStats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n      cooldownRemaining?: number;\n    }>;\n    chapterResults: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n      completedAt?: string;\n    }>;\n    model?: string;\n    concurrency?: number;\n  };\n}\n\nexport default function RewriteProgress({ jobId, onComplete }: RewriteProgressProps) {\n  const [job, setJob] = useState<JobStatus | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [hasNotifiedCompletion, setHasNotifiedCompletion] = useState(false);\n  const [initialJobStatus, setInitialJobStatus] = useState<string | null>(null);\n  const [showDiagnostics, setShowDiagnostics] = useState(false);\n\n  useEffect(() => {\n    // 重置状态当jobId改变时\n    setHasNotifiedCompletion(false);\n    setInitialJobStatus(null);\n    setLoading(true);\n    setJob(null);\n\n    const interval = setInterval(checkJobStatus, 2000); // 每2秒检查一次状态\n    checkJobStatus(); // 立即检查一次\n\n    return () => clearInterval(interval);\n  }, [jobId]);\n\n  const checkJobStatus = async () => {\n    try {\n      const response = await fetch(`/api/jobs?jobId=${jobId}`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      if (result && result.success && result.data) {\n        const newJob = result.data;\n\n        // 记录初始状态（只在第一次获取时）\n        if (initialJobStatus === null) {\n          setInitialJobStatus(newJob.status);\n        }\n\n        setJob(newJob);\n        setLoading(false);\n\n        // 只有当任务从非完成状态变为完成状态时才通知\n        // 避免对已经完成的任务重复通知\n        const isNewlyCompleted = (newJob.status === 'completed' || newJob.status === 'failed') &&\n                                 initialJobStatus !== null &&\n                                 initialJobStatus !== 'completed' &&\n                                 initialJobStatus !== 'failed' &&\n                                 !hasNotifiedCompletion;\n\n        if (isNewlyCompleted) {\n          setHasNotifiedCompletion(true);\n          setTimeout(() => {\n            onComplete();\n          }, 2000); // 2秒后通知完成\n        }\n      } else {\n        console.error('获取任务状态失败:', result?.error || '响应格式错误');\n      }\n    } catch (error) {\n      console.error('获取任务状态失败:', error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"text-yellow-500\" size={20} />;\n      case 'processing':\n        return <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500\"></div>;\n      case 'completed':\n        return <CheckCircle className=\"text-green-500\" size={20} />;\n      case 'failed':\n        return <XCircle className=\"text-red-500\" size={20} />;\n      default:\n        return <AlertCircle className=\"text-gray-500\" size={20} />;\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return '等待处理';\n      case 'processing':\n        return '正在改写';\n      case 'completed':\n        return '改写完成';\n      case 'failed':\n        return '改写失败';\n      default:\n        return '未知状态';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n      case 'processing':\n        return 'text-blue-600 bg-blue-50 border-blue-200';\n      case 'completed':\n        return 'text-green-600 bg-green-50 border-green-200';\n      case 'failed':\n        return 'text-red-600 bg-red-50 border-red-200';\n      default:\n        return 'text-gray-600 bg-gray-50 border-gray-200';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">获取任务状态中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!job) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4 text-red-600\">\n          <XCircle className=\"mx-auto mb-2\" size={32} />\n          <p>无法获取任务状态</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-800\">改写进度</h3>\n        {/* 诊断按钮 */}\n        {(job.status === 'completed' || job.status === 'failed') && (\n          <button\n            onClick={() => setShowDiagnostics(true)}\n            className=\"flex items-center gap-1 px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded transition-colors\"\n          >\n            <Activity className=\"w-4 h-4\" />\n            诊断\n          </button>\n        )}\n      </div>\n\n      {/* 状态显示 */}\n      <div className={`p-4 rounded-lg border ${getStatusColor(job.status)} mb-4`}>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            {getStatusIcon(job.status)}\n            <span className=\"ml-2 font-medium\">{getStatusText(job.status)}</span>\n          </div>\n          <span className=\"text-sm\">\n            {job.progress}%\n          </span>\n        </div>\n      </div>\n\n      {/* 进度条 */}\n      <div className=\"mb-4\">\n        <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n          <span>进度</span>\n          <span>{job.progress}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div\n            className={`h-2 rounded-full transition-all duration-300 ${\n              job.status === 'completed'\n                ? 'bg-green-500'\n                : job.status === 'failed'\n                ? 'bg-red-500'\n                : 'bg-blue-500'\n            }`}\n            style={{ width: `${job.progress}%` }}\n          ></div>\n        </div>\n      </div>\n\n      {/* 详细统计信息 */}\n      {job.details && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n          {/* 章节统计 */}\n          <div className=\"bg-gray-50 p-3 rounded-lg\">\n            <h4 className=\"font-medium text-gray-800 mb-2\">章节统计</h4>\n            <div className=\"text-sm text-gray-600 space-y-1\">\n              <div>总章节: {job.details.totalChapters}</div>\n              <div>已完成: {job.details.completedChapters}</div>\n              <div>失败: {job.details.failedChapters}</div>\n              <div>剩余: {job.details.totalChapters - job.details.completedChapters - job.details.failedChapters}</div>\n            </div>\n          </div>\n\n          {/* 性能统计 */}\n          <div className=\"bg-gray-50 p-3 rounded-lg\">\n            <h4 className=\"font-medium text-gray-800 mb-2\">性能统计</h4>\n            <div className=\"text-sm text-gray-600 space-y-1\">\n              <div>总耗时: {Math.round(job.details.totalProcessingTime / 1000)}秒</div>\n              <div>平均每章: {Math.round(job.details.averageTimePerChapter / 1000)}秒</div>\n              <div>Token消耗: {job.details.totalTokensUsed.toLocaleString()}</div>\n              <div>模型: {job.details.model}</div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* API Key状态 */}\n      {job.details?.apiKeyStats && job.details.apiKeyStats.length > 0 && (\n        <div className=\"mb-4\">\n          <h4 className=\"font-medium text-gray-800 mb-2\">API Key 使用状态</h4>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2\">\n            {job.details.apiKeyStats.map((keyStats, index) => (\n              <div key={index} className={`p-2 rounded border text-xs ${\n                keyStats.isAvailable ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'\n              }`}>\n                <div className=\"font-medium\">{keyStats.name}</div>\n                <div>权重: {keyStats.weight}x</div>\n                <div>使用次数: {keyStats.requestCount}</div>\n                <div className={keyStats.isAvailable ? 'text-green-600' : 'text-red-600'}>\n                  {keyStats.isAvailable ? '可用' : '冷却中'}\n                </div>\n                {keyStats.cooldownRemaining && keyStats.cooldownRemaining > 0 && (\n                  <div className=\"text-red-500\">\n                    冷却: {Math.round(keyStats.cooldownRemaining / 1000)}s\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 时间信息 */}\n      <div className=\"text-sm text-gray-500 space-y-1\">\n        <div>开始时间: {new Date(job.createdAt).toLocaleString()}</div>\n        <div>更新时间: {new Date(job.updatedAt).toLocaleString()}</div>\n        {job.details?.totalProcessingTime && job.status === 'completed' && (\n          <div>总耗时: {Math.round(job.details.totalProcessingTime / 1000)} 秒</div>\n        )}\n      </div>\n\n      {/* 结果信息 */}\n      {job.result && (\n        <div className=\"mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <h4 className=\"font-medium text-gray-800 mb-2\">结果信息</h4>\n          <p className=\"text-sm text-gray-700 whitespace-pre-wrap\">{job.result}</p>\n        </div>\n      )}\n\n      {/* 操作提示 */}\n      {job.status === 'completed' && (\n        <div className=\"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg\">\n          <div className=\"flex items-center text-green-700\">\n            <CheckCircle className=\"mr-2\" size={16} />\n            <span className=\"text-sm\">\n              改写完成！改写后的文件已保存到 data/rewritten 目录中。\n            </span>\n          </div>\n        </div>\n      )}\n\n      {job.status === 'failed' && (\n        <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n          <div className=\"flex items-center text-red-700\">\n            <XCircle className=\"mr-2\" size={16} />\n            <span className=\"text-sm\">\n              改写失败，请检查错误信息并重试。\n            </span>\n          </div>\n        </div>\n      )}\n\n      {job.status === 'processing' && (\n        <div className=\"mt-4 space-y-3\">\n          <div className=\"p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n            <div className=\"flex items-center text-blue-700\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2\"></div>\n              <span className=\"text-sm\">\n                正在使用 Gemini AI 改写章节，请耐心等待...\n              </span>\n            </div>\n            {job.details && (\n              <div className=\"mt-2 text-xs text-blue-600\">\n                并发数: {job.details.concurrency} | 模型: {job.details.model}\n              </div>\n            )}\n          </div>\n\n          {/* 最近完成的章节 */}\n          {job.details?.chapterResults && job.details.chapterResults.length > 0 && (\n            <div className=\"bg-gray-50 p-3 rounded-lg\">\n              <h4 className=\"font-medium text-gray-800 mb-2\">最近完成的章节</h4>\n              <div className=\"max-h-32 overflow-y-auto space-y-1\">\n                {job.details.chapterResults\n                  .filter(result => result && result.completedAt)\n                  .sort((a, b) => new Date(b.completedAt!).getTime() - new Date(a.completedAt!).getTime())\n                  .slice(0, 5)\n                  .map((result, index) => (\n                    <div key={index} className={`text-xs p-2 rounded ${\n                      result.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'\n                    }`}>\n                      <div className=\"flex justify-between items-center\">\n                        <span>第{result.chapterNumber}章: {result.chapterTitle}</span>\n                        <span>{result.success ? '✓' : '✗'}</span>\n                      </div>\n                      <div className=\"flex justify-between text-xs opacity-75\">\n                        <span>{result.apiKeyUsed}</span>\n                        <span>{result.processingTime ? Math.round(result.processingTime / 1000) + 's' : ''}</span>\n                        <span>{result.tokensUsed ? result.tokensUsed + ' tokens' : ''}</span>\n                      </div>\n                      {result.error && (\n                        <div className=\"text-red-600 text-xs mt-1\">{result.error}</div>\n                      )}\n                    </div>\n                  ))}\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 失败章节重试组件 */}\n      {job.status === 'completed' && job.details?.chapterResults && (\n        <FailedChaptersRetry\n          jobId={job.id}\n          failedChapters={job.details.chapterResults\n            .filter(result => result && !result.success)\n            .map(result => ({\n              chapterNumber: result.chapterNumber,\n              chapterTitle: result.chapterTitle,\n              error: result.error,\n              apiKeyUsed: result.apiKeyUsed,\n              processingTime: result.processingTime,\n              detailedError: (result as any).detailedError, // 类型断言，因为接口可能还没更新\n              debugInfo: (result as any).debugInfo, // 新增调试信息\n            }))}\n          rules={''} // 这里需要从父组件传入原始规则\n          model={job.details.model}\n          onRetryStart={() => {\n            // 重试开始时的回调\n            console.log('开始重试失败章节');\n          }}\n          onRetryComplete={(success, message) => {\n            // 重试完成时的回调\n            console.log('重试完成:', success, message);\n            if (success) {\n              // 刷新任务状态\n              fetchJobStatus();\n            }\n          }}\n        />\n      )}\n\n      {/* 诊断面板 */}\n      <DiagnosticsPanel\n        jobId={job.id}\n        isOpen={showDiagnostics}\n        onClose={() => setShowDiagnostics(false)}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAgDe,SAAS,gBAAgB,KAA2C;QAA3C,EAAE,KAAK,EAAE,UAAU,EAAwB,GAA3C;QA8MjC,cA6BE,eAqDE,eAiC0B;;IAhUnC,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,yKAAQ,EAAmB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,yKAAQ,EAAC;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAgB;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,yKAAQ,EAAC;IAEvD,IAAA,0KAAS;qCAAC;YACR,gBAAgB;YAChB,yBAAyB;YACzB,oBAAoB;YACpB,WAAW;YACX,OAAO;YAEP,MAAM,WAAW,YAAY,gBAAgB,OAAO,YAAY;YAChE,kBAAkB,SAAS;YAE3B;6CAAO,IAAM,cAAc;;QAC7B;oCAAG;QAAC;KAAM;IAEV,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,mBAAwB,OAAN;YAChD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YACxD;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,UAAU,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBAC3C,MAAM,SAAS,OAAO,IAAI;gBAE1B,mBAAmB;gBACnB,IAAI,qBAAqB,MAAM;oBAC7B,oBAAoB,OAAO,MAAM;gBACnC;gBAEA,OAAO;gBACP,WAAW;gBAEX,wBAAwB;gBACxB,iBAAiB;gBACjB,MAAM,mBAAmB,CAAC,OAAO,MAAM,KAAK,eAAe,OAAO,MAAM,KAAK,QAAQ,KAC5D,qBAAqB,QACrB,qBAAqB,eACrB,qBAAqB,YACrB,CAAC;gBAE1B,IAAI,kBAAkB;oBACpB,yBAAyB;oBACzB,WAAW;wBACT;oBACF,GAAG,OAAO,UAAU;gBACtB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,gNAAK;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAClD,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,0NAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD;gBACE,qBAAO,6LAAC,sOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,KAAK;QACR,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0NAAO;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCACxC,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;oBAEnD,CAAC,IAAI,MAAM,KAAK,eAAe,IAAI,MAAM,KAAK,QAAQ,mBACrD,6LAAC;wBACC,SAAS,IAAM,mBAAmB;wBAClC,WAAU;;0CAEV,6LAAC,yNAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAOtC,6LAAC;gBAAI,WAAW,AAAC,yBAAmD,OAA3B,eAAe,IAAI,MAAM,GAAE;0BAClE,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,IAAI,MAAM;8CACzB,6LAAC;oCAAK,WAAU;8CAAoB,cAAc,IAAI,MAAM;;;;;;;;;;;;sCAE9D,6LAAC;4BAAK,WAAU;;gCACb,IAAI,QAAQ;gCAAC;;;;;;;;;;;;;;;;;;0BAMpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAM,IAAI,QAAQ;oCAAC;;;;;;;;;;;;;kCAEtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAW,AAAC,gDAMX,OALC,IAAI,MAAM,KAAK,cACX,iBACA,IAAI,MAAM,KAAK,WACf,eACA;4BAEN,OAAO;gCAAE,OAAO,AAAC,GAAe,OAAb,IAAI,QAAQ,EAAC;4BAAG;;;;;;;;;;;;;;;;;YAMxC,IAAI,OAAO,kBACV,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAI;4CAAM,IAAI,OAAO,CAAC,aAAa;;;;;;;kDACpC,6LAAC;;4CAAI;4CAAM,IAAI,OAAO,CAAC,iBAAiB;;;;;;;kDACxC,6LAAC;;4CAAI;4CAAK,IAAI,OAAO,CAAC,cAAc;;;;;;;kDACpC,6LAAC;;4CAAI;4CAAK,IAAI,OAAO,CAAC,aAAa,GAAG,IAAI,OAAO,CAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;kCAKpG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAI;4CAAM,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,mBAAmB,GAAG;4CAAM;;;;;;;kDAC9D,6LAAC;;4CAAI;4CAAO,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,qBAAqB,GAAG;4CAAM;;;;;;;kDACjE,6LAAC;;4CAAI;4CAAU,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc;;;;;;;kDACzD,6LAAC;;4CAAI;4CAAK,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;YAOlC,EAAA,eAAA,IAAI,OAAO,cAAX,mCAAA,aAAa,WAAW,KAAI,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,mBAC5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,6LAAC;wBAAI,WAAU;kCACZ,IAAI,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,sBACtC,6LAAC;gCAAgB,WAAW,AAAC,8BAE5B,OADC,SAAS,WAAW,GAAG,iCAAiC;;kDAExD,6LAAC;wCAAI,WAAU;kDAAe,SAAS,IAAI;;;;;;kDAC3C,6LAAC;;4CAAI;4CAAK,SAAS,MAAM;4CAAC;;;;;;;kDAC1B,6LAAC;;4CAAI;4CAAO,SAAS,YAAY;;;;;;;kDACjC,6LAAC;wCAAI,WAAW,SAAS,WAAW,GAAG,mBAAmB;kDACvD,SAAS,WAAW,GAAG,OAAO;;;;;;oCAEhC,SAAS,iBAAiB,IAAI,SAAS,iBAAiB,GAAG,mBAC1D,6LAAC;wCAAI,WAAU;;4CAAe;4CACvB,KAAK,KAAK,CAAC,SAAS,iBAAiB,GAAG;4CAAM;;;;;;;;+BAX/C;;;;;;;;;;;;;;;;0BAqBlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAI;4BAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;kCAClD,6LAAC;;4BAAI;4BAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;oBACjD,EAAA,gBAAA,IAAI,OAAO,cAAX,oCAAA,cAAa,mBAAmB,KAAI,IAAI,MAAM,KAAK,6BAClD,6LAAC;;4BAAI;4BAAM,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,mBAAmB,GAAG;4BAAM;;;;;;;;;;;;;YAKjE,IAAI,MAAM,kBACT,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,6LAAC;wBAAE,WAAU;kCAA6C,IAAI,MAAM;;;;;;;;;;;;YAKvE,IAAI,MAAM,KAAK,6BACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6OAAW;4BAAC,WAAU;4BAAO,MAAM;;;;;;sCACpC,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;YAO/B,IAAI,MAAM,KAAK,0BACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,0NAAO;4BAAC,WAAU;4BAAO,MAAM;;;;;;sCAChC,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;YAO/B,IAAI,MAAM,KAAK,8BACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;4BAI3B,IAAI,OAAO,kBACV,6LAAC;gCAAI,WAAU;;oCAA6B;oCACpC,IAAI,OAAO,CAAC,WAAW;oCAAC;oCAAQ,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;;;oBAM5D,EAAA,gBAAA,IAAI,OAAO,cAAX,oCAAA,cAAa,cAAc,KAAI,IAAI,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,mBAClE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAI,WAAU;0CACZ,IAAI,OAAO,CAAC,cAAc,CACxB,MAAM,CAAC,CAAA,SAAU,UAAU,OAAO,WAAW,EAC7C,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAG,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAG,OAAO,IACpF,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,QAAQ,sBACZ,6LAAC;wCAAgB,WAAW,AAAC,uBAE5B,OADC,OAAO,OAAO,GAAG,gCAAgC;;0DAEjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAK;4DAAE,OAAO,aAAa;4DAAC;4DAAI,OAAO,YAAY;;;;;;;kEACpD,6LAAC;kEAAM,OAAO,OAAO,GAAG,MAAM;;;;;;;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,OAAO,UAAU;;;;;;kEACxB,6LAAC;kEAAM,OAAO,cAAc,GAAG,KAAK,KAAK,CAAC,OAAO,cAAc,GAAG,QAAQ,MAAM;;;;;;kEAChF,6LAAC;kEAAM,OAAO,UAAU,GAAG,OAAO,UAAU,GAAG,YAAY;;;;;;;;;;;;4CAE5D,OAAO,KAAK,kBACX,6LAAC;gDAAI,WAAU;0DAA6B,OAAO,KAAK;;;;;;;uCAblD;;;;;;;;;;;;;;;;;;;;;;YAwBvB,IAAI,MAAM,KAAK,iBAAe,gBAAA,IAAI,OAAO,cAAX,oCAAA,cAAa,cAAc,mBACxD,6LAAC,uJAAmB;gBAClB,OAAO,IAAI,EAAE;gBACb,gBAAgB,IAAI,OAAO,CAAC,cAAc,CACvC,MAAM,CAAC,CAAA,SAAU,UAAU,CAAC,OAAO,OAAO,EAC1C,GAAG,CAAC,CAAA,SAAU,CAAC;wBACd,eAAe,OAAO,aAAa;wBACnC,cAAc,OAAO,YAAY;wBACjC,OAAO,OAAO,KAAK;wBACnB,YAAY,OAAO,UAAU;wBAC7B,gBAAgB,OAAO,cAAc;wBACrC,eAAe,AAAC,OAAe,aAAa;wBAC5C,WAAW,AAAC,OAAe,SAAS;oBACtC,CAAC;gBACH,OAAO;gBACP,OAAO,IAAI,OAAO,CAAC,KAAK;gBACxB,cAAc;oBACZ,WAAW;oBACX,QAAQ,GAAG,CAAC;gBACd;gBACA,iBAAiB,CAAC,SAAS;oBACzB,WAAW;oBACX,QAAQ,GAAG,CAAC,SAAS,SAAS;oBAC9B,IAAI,SAAS;wBACX,SAAS;wBACT;oBACF;gBACF;;;;;;0BAKJ,6LAAC,oJAAgB;gBACf,OAAO,IAAI,EAAE;gBACb,QAAQ;gBACR,SAAS,IAAM,mBAAmB;;;;;;;;;;;;AAI1C;GAxWwB;KAAA", "debugId": null}}, {"offset": {"line": 4476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/JobHistory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { CheckCircle, XCircle, Clock, AlertCircle, Eye, Trash2, RefreshCw } from 'lucide-react';\n\ninterface JobHistoryProps {\n  onJobSelect?: (jobId: string) => void;\n}\n\ninterface JobSummary {\n  id: string;\n  novelId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    model?: string;\n  };\n}\n\nexport default function JobHistory({ onJobSelect }: JobHistoryProps) {\n  const [jobs, setJobs] = useState<JobSummary[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [novels, setNovels] = useState<Record<string, string>>({});\n\n  useEffect(() => {\n    loadJobs();\n    loadNovels();\n  }, []);\n\n  const loadJobs = async () => {\n    try {\n      const response = await fetch('/api/jobs');\n      const result = await response.json();\n      \n      if (result.success) {\n        // 按创建时间倒序排列\n        const sortedJobs = result.data.sort((a: JobSummary, b: JobSummary) => \n          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n        );\n        setJobs(sortedJobs);\n      }\n    } catch (error) {\n      console.error('加载任务历史失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadNovels = async () => {\n    try {\n      const response = await fetch('/api/novels');\n      const result = await response.json();\n      \n      if (result.success) {\n        const novelMap: Record<string, string> = {};\n        // result.data 包含 novels 和 availableFiles，我们需要 novels 数组\n        const novels = result.data.novels || [];\n        if (Array.isArray(novels)) {\n          novels.forEach((novel: any) => {\n            novelMap[novel.id] = novel.title;\n          });\n        }\n        setNovels(novelMap);\n      }\n    } catch (error) {\n      console.error('加载小说列表失败:', error);\n    }\n  };\n\n  const deleteJob = async (jobId: string) => {\n    if (!confirm('确定要删除这个任务记录吗？')) return;\n    \n    try {\n      const response = await fetch(`/api/jobs?jobId=${jobId}`, {\n        method: 'DELETE',\n      });\n      \n      if (response.ok) {\n        setJobs(jobs.filter(job => job.id !== jobId));\n      }\n    } catch (error) {\n      console.error('删除任务失败:', error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"text-yellow-500\" size={16} />;\n      case 'processing':\n        return <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"></div>;\n      case 'completed':\n        return <CheckCircle className=\"text-green-500\" size={16} />;\n      case 'failed':\n        return <XCircle className=\"text-red-500\" size={16} />;\n      default:\n        return <AlertCircle className=\"text-gray-500\" size={16} />;\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'pending': return '等待中';\n      case 'processing': return '处理中';\n      case 'completed': return '已完成';\n      case 'failed': return '失败';\n      default: return '未知';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending': return 'bg-yellow-50 border-yellow-200';\n      case 'processing': return 'bg-blue-50 border-blue-200';\n      case 'completed': return 'bg-green-50 border-green-200';\n      case 'failed': return 'bg-red-50 border-red-200';\n      default: return 'bg-gray-50 border-gray-200';\n    }\n  };\n\n  const formatDuration = (ms: number) => {\n    const seconds = Math.round(ms / 1000);\n    if (seconds < 60) return `${seconds}秒`;\n    const minutes = Math.round(seconds / 60);\n    if (minutes < 60) return `${minutes}分钟`;\n    const hours = Math.round(minutes / 60);\n    return `${hours}小时`;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">加载任务历史中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex justify-between items-center\">\n          <h3 className=\"text-lg font-semibold text-gray-800\">任务历史</h3>\n          <button\n            onClick={loadJobs}\n            className=\"flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800\"\n          >\n            <RefreshCw size={14} className=\"mr-1\" />\n            刷新\n          </button>\n        </div>\n      </div>\n\n      <div className=\"max-h-96 overflow-y-auto\">\n        {jobs.length === 0 ? (\n          <div className=\"p-6 text-center text-gray-500\">\n            暂无任务记录\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {jobs.map((job) => (\n              <div key={job.id} className=\"p-4 hover:bg-gray-50\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center mb-2\">\n                      {getStatusIcon(job.status)}\n                      <span className=\"ml-2 font-medium text-gray-800\">\n                        {novels[job.novelId] || '未知小说'}\n                      </span>\n                      <span className={`ml-2 px-2 py-1 text-xs rounded border ${getStatusColor(job.status)}`}>\n                        {getStatusText(job.status)}\n                      </span>\n                    </div>\n                    \n                    <div className=\"text-sm text-gray-600 space-y-1\">\n                      <div>创建时间: {new Date(job.createdAt).toLocaleString()}</div>\n                      {job.details && (\n                        <div className=\"flex space-x-4\">\n                          <span>章节: {job.details.completedChapters}/{job.details.totalChapters}</span>\n                          {job.details.totalTokensUsed > 0 && (\n                            <span>Token: {job.details.totalTokensUsed.toLocaleString()}</span>\n                          )}\n                          {job.details.totalProcessingTime > 0 && (\n                            <span>耗时: {formatDuration(job.details.totalProcessingTime)}</span>\n                          )}\n                          {job.details.model && (\n                            <span>模型: {job.details.model}</span>\n                          )}\n                        </div>\n                      )}\n                      {job.status !== 'completed' && job.status !== 'failed' && (\n                        <div>进度: {job.progress}%</div>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2 ml-4\">\n                    {onJobSelect && (\n                      <button\n                        onClick={() => onJobSelect(job.id)}\n                        className=\"p-1 text-blue-600 hover:text-blue-800\"\n                        title=\"查看详情\"\n                      >\n                        <Eye size={16} />\n                      </button>\n                    )}\n                    <button\n                      onClick={() => deleteJob(job.id)}\n                      className=\"p-1 text-red-600 hover:text-red-800\"\n                      title=\"删除记录\"\n                    >\n                      <Trash2 size={16} />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA2Be,SAAS,WAAW,KAAgC;QAAhC,EAAE,WAAW,EAAmB,GAAhC;;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAe,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAyB,CAAC;IAE9D,IAAA,0KAAS;gCAAC;YACR;YACA;QACF;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY;gBACZ,MAAM,aAAa,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,GAAe,IAClD,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBAEjE,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,WAAmC,CAAC;gBAC1C,wDAAwD;gBACxD,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE;gBACvC,IAAI,MAAM,OAAO,CAAC,SAAS;oBACzB,OAAO,OAAO,CAAC,CAAC;wBACd,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,KAAK;oBAClC;gBACF;gBACA,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,YAAY,OAAO;QACvB,IAAI,CAAC,QAAQ,kBAAkB;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,mBAAwB,OAAN,QAAS;gBACvD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,gNAAK;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAClD,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,0NAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD;gBACE,qBAAO,6LAAC,sOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK;QAChC,IAAI,UAAU,IAAI,OAAO,AAAC,GAAU,OAAR,SAAQ;QACpC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,IAAI,UAAU,IAAI,OAAO,AAAC,GAAU,OAAR,SAAQ;QACpC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,OAAO,AAAC,GAAQ,OAAN,OAAM;IAClB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,gOAAS;oCAAC,MAAM;oCAAI,WAAU;;;;;;gCAAS;;;;;;;;;;;;;;;;;;0BAM9C,6LAAC;gBAAI,WAAU;0BACZ,KAAK,MAAM,KAAK,kBACf,6LAAC;oBAAI,WAAU;8BAAgC;;;;;yCAI/C,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;4BAAiB,WAAU;sCAC1B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,IAAI,MAAM;kEACzB,6LAAC;wDAAK,WAAU;kEACb,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI;;;;;;kEAE1B,6LAAC;wDAAK,WAAW,AAAC,yCAAmE,OAA3B,eAAe,IAAI,MAAM;kEAChF,cAAc,IAAI,MAAM;;;;;;;;;;;;0DAI7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAI;4DAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;oDACjD,IAAI,OAAO,kBACV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAK;oEAAK,IAAI,OAAO,CAAC,iBAAiB;oEAAC;oEAAE,IAAI,OAAO,CAAC,aAAa;;;;;;;4DACnE,IAAI,OAAO,CAAC,eAAe,GAAG,mBAC7B,6LAAC;;oEAAK;oEAAQ,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc;;;;;;;4DAEzD,IAAI,OAAO,CAAC,mBAAmB,GAAG,mBACjC,6LAAC;;oEAAK;oEAAK,eAAe,IAAI,OAAO,CAAC,mBAAmB;;;;;;;4DAE1D,IAAI,OAAO,CAAC,KAAK,kBAChB,6LAAC;;oEAAK;oEAAK,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;;;oDAIjC,IAAI,MAAM,KAAK,eAAe,IAAI,MAAM,KAAK,0BAC5C,6LAAC;;4DAAI;4DAAK,IAAI,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;kDAK7B,6LAAC;wCAAI,WAAU;;4CACZ,6BACC,6LAAC;gDACC,SAAS,IAAM,YAAY,IAAI,EAAE;gDACjC,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,0MAAG;oDAAC,MAAM;;;;;;;;;;;0DAGf,6LAAC;gDACC,SAAS,IAAM,UAAU,IAAI,EAAE;gDAC/B,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,uNAAM;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;2BAlDZ,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;AA6D9B;GA7MwB;KAAA", "debugId": null}}, {"offset": {"line": 4928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/ApiKeyStats.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { RefreshCw, Key, Activity, Clock, CheckCircle, XCircle } from 'lucide-react';\n\ninterface ApiKeyStatsProps {\n  refreshInterval?: number; // 刷新间隔（毫秒）\n}\n\ninterface ApiKeyStats {\n  name: string;\n  requestCount: number;\n  weight: number;\n  isAvailable: boolean;\n  cooldownRemaining?: number;\n}\n\nexport default function ApiKeyStats({ refreshInterval = 5000 }: ApiKeyStatsProps) {\n  const [stats, setStats] = useState<ApiKeyStats[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);\n\n  useEffect(() => {\n    loadStats();\n    \n    if (refreshInterval > 0) {\n      const interval = setInterval(loadStats, refreshInterval);\n      return () => clearInterval(interval);\n    }\n  }, [refreshInterval]);\n\n  const loadStats = async () => {\n    try {\n      const response = await fetch('/api/gemini/stats');\n      const result = await response.json();\n      \n      if (result.success) {\n        setStats(result.data);\n        setLastUpdated(new Date());\n      }\n    } catch (error) {\n      console.error('加载API统计失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testConnection = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/gemini/test');\n      const result = await response.json();\n      \n      if (result.success) {\n        alert(`连接测试成功！\\n使用的API Key: ${result.details?.apiKeyUsed}\\nToken消耗: ${result.details?.tokensUsed}\\n处理时间: ${result.details?.processingTime}ms`);\n      } else {\n        alert(`连接测试失败: ${result.error}`);\n      }\n      \n      // 测试后刷新统计\n      await loadStats();\n    } catch (error) {\n      alert(`连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetStats = async () => {\n    if (!confirm('确定要重置所有API Key统计吗？')) return;\n    \n    try {\n      const response = await fetch('/api/gemini/reset', {\n        method: 'POST',\n      });\n      \n      if (response.ok) {\n        await loadStats();\n        alert('统计已重置');\n      }\n    } catch (error) {\n      alert(`重置失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  };\n\n  const formatCooldown = (ms: number) => {\n    const seconds = Math.ceil(ms / 1000);\n    if (seconds < 60) return `${seconds}秒`;\n    const minutes = Math.ceil(seconds / 60);\n    return `${minutes}分钟`;\n  };\n\n  if (loading && stats.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">加载API统计中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <Key className=\"mr-2 text-blue-600\" size={20} />\n            <h3 className=\"text-lg font-semibold text-gray-800\">API Key 状态</h3>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {lastUpdated && (\n              <span className=\"text-xs text-gray-500\">\n                更新于 {lastUpdated.toLocaleTimeString()}\n              </span>\n            )}\n            <button\n              onClick={loadStats}\n              disabled={loading}\n              className=\"flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50\"\n            >\n              <RefreshCw size={14} className={`mr-1 ${loading ? 'animate-spin' : ''}`} />\n              刷新\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {stats.length === 0 ? (\n          <div className=\"text-center text-gray-500 py-4\">\n            暂无API Key统计数据\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {/* 总体统计 */}\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n              <div className=\"bg-blue-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <Activity className=\"text-blue-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-blue-600\">总请求数</div>\n                    <div className=\"text-lg font-semibold text-blue-800\">\n                      {stats.reduce((sum, stat) => sum + stat.requestCount, 0)}\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-green-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"text-green-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-green-600\">可用Key</div>\n                    <div className=\"text-lg font-semibold text-green-800\">\n                      {stats.filter(stat => stat.isAvailable).length}\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-red-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <XCircle className=\"text-red-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-red-600\">冷却中</div>\n                    <div className=\"text-lg font-semibold text-red-800\">\n                      {stats.filter(stat => !stat.isAvailable).length}\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-purple-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <Key className=\"text-purple-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-purple-600\">总权重</div>\n                    <div className=\"text-lg font-semibold text-purple-800\">\n                      {stats.reduce((sum, stat) => sum + stat.weight, 0)}x\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* 详细Key状态 */}\n            <div className=\"space-y-3\">\n              {stats.map((stat, index) => (\n                <div key={index} className={`p-4 rounded-lg border ${\n                  stat.isAvailable \n                    ? 'bg-green-50 border-green-200' \n                    : 'bg-red-50 border-red-200'\n                }`}>\n                  <div className=\"flex justify-between items-center\">\n                    <div className=\"flex items-center\">\n                      <div className={`w-3 h-3 rounded-full mr-3 ${\n                        stat.isAvailable ? 'bg-green-500' : 'bg-red-500'\n                      }`}></div>\n                      <div>\n                        <div className=\"font-medium text-gray-800\">{stat.name}</div>\n                        <div className=\"text-sm text-gray-600\">\n                          权重: {stat.weight}x | 使用次数: {stat.requestCount}\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"text-right\">\n                      <div className={`text-sm font-medium ${\n                        stat.isAvailable ? 'text-green-600' : 'text-red-600'\n                      }`}>\n                        {stat.isAvailable ? '可用' : '冷却中'}\n                      </div>\n                      {!stat.isAvailable && stat.cooldownRemaining && stat.cooldownRemaining > 0 && (\n                        <div className=\"text-xs text-red-500 flex items-center\">\n                          <Clock size={12} className=\"mr-1\" />\n                          {formatCooldown(stat.cooldownRemaining)}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* 操作按钮 */}\n        <div className=\"flex justify-center space-x-4 mt-6 pt-4 border-t border-gray-200\">\n          <button\n            onClick={testConnection}\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? '测试中...' : '测试连接'}\n          </button>\n          \n          <button\n            onClick={resetStats}\n            className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700\"\n          >\n            重置统计\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAiBe,SAAS,YAAY,KAA4C;QAA5C,EAAE,kBAAkB,IAAI,EAAoB,GAA5C;;IAClC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAgB,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAc;IAE5D,IAAA,0KAAS;iCAAC;YACR;YAEA,IAAI,kBAAkB,GAAG;gBACvB,MAAM,WAAW,YAAY,WAAW;gBACxC;6CAAO,IAAM,cAAc;;YAC7B;QACF;gCAAG;QAAC;KAAgB;IAEpB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,OAAO,IAAI;gBACpB,eAAe,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;QAC9B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;oBACY,iBAAwC,kBAAqC;gBAA3G,MAAM,AAtDd,AAsDe,gCAAuB,kBAAA,OAAO,OAAO,cAAd,sCAAA,gBAAgB,UAAU,EAAC,uBAAa,mBAAA,OAAO,OAAO,cAAd,uCAAA,iBAAgB,UAAU,EAAC,YAAyC,QAA/B,mBAAA,OAAO,OAAO,cAAd,uCAAA,iBAAgB,cAAc,EAAC;YAC5I,OAAO;gBACL,MAAM,AAAC,WAAuB,OAAb,OAAO,KAAK;YAC/B;YAEA,UAAU;YACV,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM,AAAC,WAA0D,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC5D,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,uBAAuB;QAEpC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM,AAAC,SAAwD,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC1D;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK;QAC/B,IAAI,UAAU,IAAI,OAAO,AAAC,GAAU,OAAR,SAAQ;QACpC,MAAM,UAAU,KAAK,IAAI,CAAC,UAAU;QACpC,OAAO,AAAC,GAAU,OAAR,SAAQ;IACpB;IAEA,IAAI,WAAW,MAAM,MAAM,KAAK,GAAG;QACjC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0MAAG;oCAAC,WAAU;oCAAqB,MAAM;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;;gCACZ,6BACC,6LAAC;oCAAK,WAAU;;wCAAwB;wCACjC,YAAY,kBAAkB;;;;;;;8CAGvC,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC,gOAAS;4CAAC,MAAM;4CAAI,WAAW,AAAC,QAAqC,OAA9B,UAAU,iBAAiB;;;;;;wCAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAOnF,6LAAC;gBAAI,WAAU;;oBACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;wBAAI,WAAU;kCAAiC;;;;;6CAIhD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yNAAQ;oDAAC,WAAU;oDAAqB,MAAM;;;;;;8DAC/C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,6LAAC;4DAAI,WAAU;sEACZ,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAM9D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6OAAW;oDAAC,WAAU;oDAAsB,MAAM;;;;;;8DACnD,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAyB;;;;;;sEACxC,6LAAC;4DAAI,WAAU;sEACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAMtD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,0NAAO;oDAAC,WAAU;oDAAoB,MAAM;;;;;;8DAC7C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAuB;;;;;;sEACtC,6LAAC;4DAAI,WAAU;sEACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAMvD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,0MAAG;oDAAC,WAAU;oDAAuB,MAAM;;;;;;8DAC5C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAA0B;;;;;;sEACzC,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7D,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wCAAgB,WAAW,AAAC,yBAI5B,OAHC,KAAK,WAAW,GACZ,iCACA;kDAEJ,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,6BAEhB,OADC,KAAK,WAAW,GAAG,iBAAiB;;;;;;sEAEtC,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAA6B,KAAK,IAAI;;;;;;8EACrD,6LAAC;oEAAI,WAAU;;wEAAwB;wEAChC,KAAK,MAAM;wEAAC;wEAAW,KAAK,YAAY;;;;;;;;;;;;;;;;;;;8DAKnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,uBAEhB,OADC,KAAK,WAAW,GAAG,mBAAmB;sEAErC,KAAK,WAAW,GAAG,OAAO;;;;;;wDAE5B,CAAC,KAAK,WAAW,IAAI,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,GAAG,mBACvE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gNAAK;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAC1B,eAAe,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;;uCA3BtC;;;;;;;;;;;;;;;;kCAuClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,UAAU,WAAW;;;;;;0CAGxB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAvOwB;KAAA", "debugId": null}}, {"offset": {"line": 5512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/TaskManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nimport RewriteProgress from './RewriteProgress';\nimport <PERSON>Hist<PERSON> from './JobHistory';\nimport ApiKeyStats from './ApiKeyStats';\nimport { Activity, History, Key, Eye } from 'lucide-react';\n\ninterface TaskManagerProps {\n  currentJobId?: string;\n  onJobComplete?: () => void;\n}\n\nexport default function TaskManager({ currentJobId, onJobComplete }: TaskManagerProps) {\n  const [selectedJobId, setSelectedJobId] = useState<string | null>(currentJobId || null);\n  const [activeTab, setActiveTab] = useState(currentJobId ? 'current' : 'history');\n\n  const handleJobSelect = (jobId: string) => {\n    setSelectedJobId(jobId);\n    setActiveTab('current');\n  };\n\n  const handleJobComplete = () => {\n    if (onJobComplete) {\n      onJobComplete();\n    }\n    // 任务完成后切换到历史页面\n    setTimeout(() => {\n      setActiveTab('history');\n    }, 2000);\n  };\n\n  const tabs = [\n    { id: 'current', label: '当前任务', icon: Activity },\n    { id: 'history', label: '任务历史', icon: History },\n    { id: 'stats', label: 'API状态', icon: Key },\n  ];\n\n  return (\n    <div className=\"w-full max-w-6xl mx-auto\">\n      {/* Tab Navigation */}\n      <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\">\n        {tabs.map((tab) => {\n          const Icon = tab.icon;\n          return (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors flex-1 justify-center ${\n                activeTab === tab.id\n                  ? 'bg-white text-blue-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-800'\n              }`}\n            >\n              <Icon className=\"mr-2\" size={16} />\n              {tab.label}\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"mt-6\">\n        {activeTab === 'current' && (\n          <div>\n            {selectedJobId ? (\n              <RewriteProgress\n                jobId={selectedJobId}\n                onComplete={handleJobComplete}\n              />\n            ) : (\n              <div className=\"bg-white rounded-lg shadow-md p-8 text-center\">\n                <Eye className=\"mx-auto mb-4 text-gray-400\" size={48} />\n                <h3 className=\"text-lg font-medium text-gray-800 mb-2\">没有正在进行的任务</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  当前没有正在执行的改写任务。你可以：\n                </p>\n                <div className=\"space-y-2 text-sm text-gray-500\">\n                  <p>• 从任务历史中选择一个任务查看详情</p>\n                  <p>• 创建新的改写任务</p>\n                  <p>• 查看API Key使用状态</p>\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'history' && (\n          <JobHistory onJobSelect={handleJobSelect} />\n        )}\n\n        {activeTab === 'stats' && (\n          <ApiKeyStats refreshInterval={5000} />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AAce,SAAS,YAAY,KAAiD;QAAjD,EAAE,YAAY,EAAE,aAAa,EAAoB,GAAjD;;IAClC,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAgB,gBAAgB;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC,eAAe,YAAY;IAEtE,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,IAAI,eAAe;YACjB;QACF;QACA,eAAe;QACf,WAAW;YACT,aAAa;QACf,GAAG;IACL;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,OAAO;YAAQ,MAAM,yNAAQ;QAAC;QAC/C;YAAE,IAAI;YAAW,OAAO;YAAQ,MAAM,sNAAO;QAAC;QAC9C;YAAE,IAAI;YAAS,OAAO;YAAS,MAAM,0MAAG;QAAC;KAC1C;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC;oBACT,MAAM,OAAO,IAAI,IAAI;oBACrB,qBACE,6LAAC;wBAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wBAClC,WAAW,AAAC,sGAIX,OAHC,cAAc,IAAI,EAAE,GAChB,qCACA;;0CAGN,6LAAC;gCAAK,WAAU;gCAAO,MAAM;;;;;;4BAC5B,IAAI,KAAK;;uBATL,IAAI,EAAE;;;;;gBAYjB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,2BACb,6LAAC;kCACE,8BACC,6LAAC,mJAAe;4BACd,OAAO;4BACP,YAAY;;;;;iDAGd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0MAAG;oCAAC,WAAU;oCAA6B,MAAM;;;;;;8CAClD,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;oBAOZ,cAAc,2BACb,6LAAC,8IAAU;wBAAC,aAAa;;;;;;oBAG1B,cAAc,yBACb,6LAAC,+IAAW;wBAAC,iBAAiB;;;;;;;;;;;;;;;;;;AAKxC;GApFwB;KAAA", "debugId": null}}, {"offset": {"line": 5717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/ModelConfigSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Setting<PERSON>, Cpu, Zap } from 'lucide-react';\n\ninterface ModelConfigSelectorProps {\n  selectedModel: string;\n  selectedConcurrency: number;\n  onModelChange: (model: string) => void;\n  onConcurrencyChange: (concurrency: number) => void;\n  disabled?: boolean;\n}\n\nconst AVAILABLE_MODELS = [\n  {\n    id: 'gemini-2.5-flash-lite',\n    name: 'Gemini 2.5 Flash Lite',\n    description: '快速、轻量级模型，适合大批量处理',\n    speed: 'fast',\n    quality: 'good'\n  },\n  {\n    id: 'gemini-2.5-flash',\n    name: 'Gemini 2.5 Flash',\n    description: '平衡速度和质量的标准模型',\n    speed: 'medium',\n    quality: 'excellent'\n  },\n  {\n    id: 'gemini-2.5-pro',\n    name: 'Gemini 2.5 Pro',\n    description: '高质量模型，处理复杂内容',\n    speed: 'slow',\n    quality: 'premium'\n  }\n];\n\nconst CONCURRENCY_OPTIONS = [\n  { value: 1, label: '1 (最安全)', description: '单线程处理，最稳定' },\n  { value: 2, label: '2 (保守)', description: '低并发，适合API限制严格的情况' },\n  { value: 3, label: '3 (推荐)', description: '默认设置，平衡速度和稳定性' },\n  { value: 4, label: '4 (积极)', description: '较高并发，需要充足的API配额' },\n  { value: 5, label: '5 (激进)', description: '高并发，适合API配额充足的情况' },\n  { value: 6, label: '6 (极限)', description: '最高并发，可能触发API限制' }\n];\n\nexport default function ModelConfigSelector({\n  selectedModel,\n  selectedConcurrency,\n  onModelChange,\n  onConcurrencyChange,\n  disabled = false\n}: ModelConfigSelectorProps) {\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  const selectedModelInfo = AVAILABLE_MODELS.find(m => m.id === selectedModel);\n  const selectedConcurrencyInfo = CONCURRENCY_OPTIONS.find(c => c.value === selectedConcurrency);\n\n  const getSpeedColor = (speed: string) => {\n    switch (speed) {\n      case 'fast': return 'text-green-600 bg-green-100';\n      case 'medium': return 'text-yellow-600 bg-yellow-100';\n      case 'slow': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getQualityColor = (quality: string) => {\n    switch (quality) {\n      case 'good': return 'text-blue-600 bg-blue-100';\n      case 'excellent': return 'text-purple-600 bg-purple-100';\n      case 'premium': return 'text-indigo-600 bg-indigo-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h2 className=\"text-lg font-semibold text-gray-800 flex items-center\">\n          <Settings className=\"mr-2\" size={18} />\n          模型配置\n        </h2>\n        <button\n          onClick={() => setIsExpanded(!isExpanded)}\n          disabled={disabled}\n          className=\"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n          title={isExpanded ? \"收起配置\" : \"展开配置\"}\n        >\n          <Zap className={`transition-transform ${isExpanded ? 'rotate-180' : ''}`} size={16} />\n        </button>\n      </div>\n\n      {/* 简化显示 */}\n      {!isExpanded && (\n        <div className=\"space-y-2\">\n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-gray-600\">当前模型:</span>\n            <span className=\"font-medium\">{selectedModelInfo?.name || selectedModel}</span>\n          </div>\n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-gray-600\">并发数:</span>\n            <span className=\"font-medium\">{selectedConcurrency}</span>\n          </div>\n        </div>\n      )}\n\n      {/* 详细配置 */}\n      {isExpanded && (\n        <div className=\"space-y-4\">\n          {/* 模型选择 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              <Cpu className=\"inline mr-1\" size={14} />\n              AI 模型\n            </label>\n            <div className=\"space-y-2\">\n              {AVAILABLE_MODELS.map((model) => (\n                <label key={model.id} className=\"flex items-start space-x-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"model\"\n                    value={model.id}\n                    checked={selectedModel === model.id}\n                    onChange={(e) => onModelChange(e.target.value)}\n                    disabled={disabled}\n                    className=\"mt-1\"\n                  />\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-medium text-gray-800\">{model.name}</span>\n                      <span className={`px-2 py-1 rounded-full text-xs ${getSpeedColor(model.speed)}`}>\n                        {model.speed === 'fast' ? '快速' : model.speed === 'medium' ? '中等' : '慢速'}\n                      </span>\n                      <span className={`px-2 py-1 rounded-full text-xs ${getQualityColor(model.quality)}`}>\n                        {model.quality === 'good' ? '良好' : model.quality === 'excellent' ? '优秀' : '顶级'}\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 mt-1\">{model.description}</p>\n                  </div>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* 并发数选择 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              <Zap className=\"inline mr-1\" size={14} />\n              并发数量\n            </label>\n            <div className=\"space-y-2\">\n              {CONCURRENCY_OPTIONS.map((option) => (\n                <label key={option.value} className=\"flex items-start space-x-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"concurrency\"\n                    value={option.value}\n                    checked={selectedConcurrency === option.value}\n                    onChange={(e) => onConcurrencyChange(parseInt(e.target.value))}\n                    disabled={disabled}\n                    className=\"mt-1\"\n                  />\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-medium text-gray-800\">{option.label}</span>\n                      {option.value === 3 && (\n                        <span className=\"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-600\">推荐</span>\n                      )}\n                    </div>\n                    <p className=\"text-sm text-gray-600 mt-1\">{option.description}</p>\n                  </div>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* 配置提示 */}\n          <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-3\">\n            <div className=\"flex items-start\">\n              <div className=\"text-yellow-600 mr-2\">⚠️</div>\n              <div className=\"text-sm text-yellow-800\">\n                <p className=\"font-medium mb-1\">配置建议:</p>\n                <ul className=\"space-y-1 text-xs\">\n                  <li>• 首次使用建议选择 &quot;Gemini 2.5 Flash Lite&quot; + 并发数 3</li>\n                  <li>• 如果遇到 429 错误，请降低并发数</li>\n                  <li>• API 配额充足时可以提高并发数以加快处理速度</li>\n                  <li>• 高质量要求的内容建议使用 &quot;Gemini 1.5 Pro&quot;</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAaA,MAAM,mBAAmB;IACvB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;IACX;CACD;AAED,MAAM,sBAAsB;IAC1B;QAAE,OAAO;QAAG,OAAO;QAAW,aAAa;IAAY;IACvD;QAAE,OAAO;QAAG,OAAO;QAAU,aAAa;IAAmB;IAC7D;QAAE,OAAO;QAAG,OAAO;QAAU,aAAa;IAAgB;IAC1D;QAAE,OAAO;QAAG,OAAO;QAAU,aAAa;IAAkB;IAC5D;QAAE,OAAO;QAAG,OAAO;QAAU,aAAa;IAAmB;IAC7D;QAAE,OAAO;QAAG,OAAO;QAAU,aAAa;IAAiB;CAC5D;AAEc,SAAS,oBAAoB,KAMjB;QANiB,EAC1C,aAAa,EACb,mBAAmB,EACnB,aAAa,EACb,mBAAmB,EACnB,WAAW,KAAK,EACS,GANiB;;IAO1C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,MAAM,oBAAoB,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC9D,MAAM,0BAA0B,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAE1E,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,yNAAQ;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGzC,6LAAC;wBACC,SAAS,IAAM,cAAc,CAAC;wBAC9B,UAAU;wBACV,WAAU;wBACV,OAAO,aAAa,SAAS;kCAE7B,cAAA,6LAAC,0MAAG;4BAAC,WAAW,AAAC,wBAAsD,OAA/B,aAAa,eAAe;4BAAM,MAAM;;;;;;;;;;;;;;;;;YAKnF,CAAC,4BACA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;0CAAe,CAAA,8BAAA,wCAAA,kBAAmB,IAAI,KAAI;;;;;;;;;;;;kCAE5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;;;;;;;YAMpC,4BACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;kDACf,6LAAC,0MAAG;wCAAC,WAAU;wCAAc,MAAM;;;;;;oCAAM;;;;;;;0CAG3C,6LAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,sBACrB,6LAAC;wCAAqB,WAAU;;0DAC9B,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,MAAM,EAAE;gDACf,SAAS,kBAAkB,MAAM,EAAE;gDACnC,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA6B,MAAM,IAAI;;;;;;0EACvD,6LAAC;gEAAK,WAAW,AAAC,kCAA4D,OAA3B,cAAc,MAAM,KAAK;0EACzE,MAAM,KAAK,KAAK,SAAS,OAAO,MAAM,KAAK,KAAK,WAAW,OAAO;;;;;;0EAErE,6LAAC;gEAAK,WAAW,AAAC,kCAAgE,OAA/B,gBAAgB,MAAM,OAAO;0EAC7E,MAAM,OAAO,KAAK,SAAS,OAAO,MAAM,OAAO,KAAK,cAAc,OAAO;;;;;;;;;;;;kEAG9E,6LAAC;wDAAE,WAAU;kEAA8B,MAAM,WAAW;;;;;;;;;;;;;uCApBpD,MAAM,EAAE;;;;;;;;;;;;;;;;kCA4B1B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;kDACf,6LAAC,0MAAG;wCAAC,WAAU;wCAAc,MAAM;;;;;;oCAAM;;;;;;;0CAG3C,6LAAC;gCAAI,WAAU;0CACZ,oBAAoB,GAAG,CAAC,CAAC,uBACxB,6LAAC;wCAAyB,WAAU;;0DAClC,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,OAAO,KAAK;gDACnB,SAAS,wBAAwB,OAAO,KAAK;gDAC7C,UAAU,CAAC,IAAM,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC5D,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA6B,OAAO,KAAK;;;;;;4DACxD,OAAO,KAAK,KAAK,mBAChB,6LAAC;gEAAK,WAAU;0EAA2D;;;;;;;;;;;;kEAG/E,6LAAC;wDAAE,WAAU;kEAA8B,OAAO,WAAW;;;;;;;;;;;;;uCAjBrD,OAAO,KAAK;;;;;;;;;;;;;;;;kCAyB9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAuB;;;;;;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAmB;;;;;;sDAChC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB;GAtJwB;KAAA", "debugId": null}}, {"offset": {"line": 6230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/Toast.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { CheckCircle, XCircle, AlertCircle, X } from 'lucide-react';\n\ninterface ToastProps {\n  message: string;\n  type: 'success' | 'error' | 'info';\n  onClose: () => void;\n  duration?: number;\n}\n\nexport default function Toast({ message, type, onClose, duration = 3000 }: ToastProps) {\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(onClose, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [duration, onClose]);\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"text-green-500\" size={20} />;\n      case 'error':\n        return <XCircle className=\"text-red-500\" size={20} />;\n      case 'info':\n        return <AlertCircle className=\"text-blue-500\" size={20} />;\n    }\n  };\n\n  const getBgColor = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200';\n      case 'error':\n        return 'bg-red-50 border-red-200';\n      case 'info':\n        return 'bg-blue-50 border-blue-200';\n    }\n  };\n\n  return (\n    <div className={`fixed top-4 right-4 z-50 max-w-sm w-full ${getBgColor()} border rounded-lg shadow-lg p-4 animate-in slide-in-from-right-full duration-300`}>\n      <div className=\"flex items-start space-x-3\">\n        {getIcon()}\n        <div className=\"flex-1 text-sm text-gray-800\">\n          {message}\n        </div>\n        <button\n          onClick={onClose}\n          className=\"text-gray-400 hover:text-gray-600\"\n        >\n          <X size={16} />\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAYe,SAAS,MAAM,KAAuD;QAAvD,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,IAAI,EAAc,GAAvD;;IAC5B,IAAA,0KAAS;2BAAC;YACR,IAAI,WAAW,GAAG;gBAChB,MAAM,QAAQ,WAAW,SAAS;gBAClC;uCAAO,IAAM,aAAa;;YAC5B;QACF;0BAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,0NAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD,KAAK;gBACH,qBAAO,6LAAC,sOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,4CAAwD,OAAb,cAAa;kBACvE,cAAA,6LAAC;YAAI,WAAU;;gBACZ;8BACD,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,oMAAC;wBAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;AAKnB;GA9CwB;KAAA", "debugId": null}}, {"offset": {"line": 6354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport NovelSelector from '@/components/NovelSelector';\nimport ChapterSelector from '@/components/ChapterSelector';\nimport RuleEditor from '@/components/RuleEditor';\nimport CharacterManager from '@/components/CharacterManager';\nimport RewriteProgress from '@/components/RewriteProgress';\nimport TaskManager from '@/components/TaskManager';\nimport ModelConfigSelector from '@/components/ModelConfigSelector';\nimport Toast from '@/components/Toast';\nimport { Novel, Chapter } from '@/lib/database';\nimport { HelpCircle } from 'lucide-react';\n\ninterface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string;\n  description: string;\n  personality?: string;\n  appearance?: string;\n  relationships?: string;\n}\n\ninterface ToastState {\n  show: boolean;\n  message: string;\n  type: 'success' | 'error' | 'info';\n}\n\nexport default function Home() {\n  const [selectedNovel, setSelectedNovel] = useState<Novel | null>(null);\n  const [selectedChapters, setSelectedChapters] = useState<string>('');\n  const [rewriteRules, setRewriteRules] = useState<string>('');\n  const [characters, setCharacters] = useState<Character[]>([]);\n  const [isRewriting, setIsRewriting] = useState(false);\n  const [currentJobId, setCurrentJobId] = useState<string | null>(null);\n  const [showTaskManager, setShowTaskManager] = useState(false);\n  const [selectedModel, setSelectedModel] = useState<string>('gemini-2.5-flash');\n  const [selectedConcurrency, setSelectedConcurrency] = useState<number>(4);\n  const [toast, setToast] = useState<ToastState>({ show: false, message: '', type: 'info' });\n\n  const showToast = (message: string, type: 'success' | 'error' | 'info') => {\n    setToast({ show: true, message, type });\n  };\n\n  const hideToast = () => {\n    setToast({ show: false, message: '', type: 'info' });\n  };\n\n  const handleSaveToPreset = async (rules: string) => {\n    const name = prompt('请输入预设名称:');\n    if (!name) return;\n\n    const description = prompt('请输入预设描述 (可选):') || '';\n\n    try {\n      const response = await fetch('/api/presets', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name,\n          description,\n          rules,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        showToast('规则已保存到预设', 'success');\n      } else {\n        showToast(`保存失败: ${result.error}`, 'error');\n      }\n    } catch (error) {\n      console.error('保存预设失败:', error);\n      showToast('保存预设失败', 'error');\n    }\n  };\n\n  const handleStartRewrite = async () => {\n    if (!selectedNovel || !selectedChapters || !rewriteRules) {\n      showToast('请完整填写所有信息', 'error');\n      return;\n    }\n\n    setIsRewriting(true);\n\n    try {\n      // 构建包含人物信息的改写规则\n      let enhancedRules = rewriteRules;\n      if (characters.length > 0) {\n        const characterInfo = characters.map(char =>\n          `${char.name}(${char.role}${char.description ? ': ' + char.description : ''})`\n        ).join('、');\n        enhancedRules = `人物设定：${characterInfo}\\n\\n${rewriteRules}`;\n      }\n\n      const response = await fetch('/api/rewrite', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          novelId: selectedNovel.id,\n          chapterRange: selectedChapters,\n          rules: enhancedRules,\n          model: selectedModel,\n          concurrency: selectedConcurrency,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      if (result && result.success) {\n        setCurrentJobId(result.data.jobId);\n        showToast('改写任务已开始', 'info');\n      } else {\n        showToast(`改写失败: ${result?.error || '未知错误'}`, 'error');\n        setIsRewriting(false);\n      }\n    } catch (error) {\n      console.error('改写请求失败:', error);\n      showToast('改写请求失败，请检查网络连接', 'error');\n      setIsRewriting(false);\n    }\n  };\n\n  const handleRewriteComplete = () => {\n    setIsRewriting(false);\n    setCurrentJobId(null);\n    showToast('改写完成！', 'success');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <h1 className=\"text-2xl font-bold text-gray-800\">\n            小说改写工具\n          </h1>\n          <div className=\"flex items-center space-x-4\">\n            {/* 任务管理按钮 */}\n            <button\n              onClick={() => setShowTaskManager(!showTaskManager)}\n              className=\"flex items-center px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\"\n            >\n              <svg className=\"mr-1\" width={18} height={18} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/>\n                <path d=\"M9 9h6v6H9z\"/>\n              </svg>\n              任务管理\n            </button>\n            {/* 开始改写按钮 */}\n            <button\n              onClick={handleStartRewrite}\n              disabled={isRewriting || !selectedNovel || !selectedChapters || !rewriteRules}\n              className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-6 rounded-lg transition-colors\"\n              title={\n                !selectedNovel ? '请先选择小说' :\n                !selectedChapters ? '请选择章节范围' :\n                !rewriteRules ? '请输入改写规则' :\n                '开始改写任务'\n              }\n            >\n              {isRewriting ? '改写中...' : '开始改写'}\n            </button>\n            <Link\n              href=\"/context\"\n              className=\"flex items-center px-3 py-2 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-lg transition-colors\"\n            >\n              <svg className=\"mr-1\" width={18} height={18} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                <path d=\"M9 12l2 2 4-4\"/>\n                <path d=\"M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3\"/>\n                <path d=\"M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3\"/>\n                <path d=\"M12 3v6m0 6v6\"/>\n              </svg>\n              上下文管理\n            </Link>\n            <Link\n              href=\"/merge\"\n              className=\"flex items-center px-3 py-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors\"\n            >\n              <svg className=\"mr-1\" width={18} height={18} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"/>\n                <polyline points=\"7,10 12,15 17,10\"/>\n                <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\"/>\n              </svg>\n              合并章节\n            </Link>\n            <Link\n              href=\"/help\"\n              className=\"flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors\"\n            >\n              <HelpCircle className=\"mr-1\" size={18} />\n              帮助\n            </Link>\n          </div>\n        </div>\n\n        {/* 任务管理器 */}\n        {showTaskManager && (\n          <div className=\"mb-6\">\n            <TaskManager\n              currentJobId={currentJobId || undefined}\n              onJobComplete={handleRewriteComplete}\n            />\n          </div>\n        )}\n\n        {/* 进度显示 */}\n        {isRewriting && currentJobId && !showTaskManager && (\n          <div className=\"mb-4\">\n            <RewriteProgress\n              jobId={currentJobId}\n              onComplete={handleRewriteComplete}\n            />\n          </div>\n        )}\n\n        {!showTaskManager && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-4\">\n          {/* 左侧：改写规则 */}\n          <div className=\"lg:col-span-1\">\n            <RuleEditor\n              rules={rewriteRules}\n              onRulesChange={setRewriteRules}\n              onSaveToPreset={handleSaveToPreset}\n              disabled={isRewriting}\n            />\n          </div>\n\n          {/* 中间左：小说选择和人物管理 */}\n          <div className=\"lg:col-span-1 space-y-4\">\n            <NovelSelector\n              selectedNovel={selectedNovel}\n              onNovelSelect={setSelectedNovel}\n              disabled={isRewriting}\n            />\n            <CharacterManager\n              novelId={selectedNovel?.id}\n              characters={characters}\n              onCharactersChange={setCharacters}\n              disabled={isRewriting}\n            />\n          </div>\n\n          {/* 中间右：章节选择 */}\n          <div className=\"lg:col-span-1\">\n            <ChapterSelector\n              novel={selectedNovel}\n              selectedChapters={selectedChapters}\n              onChaptersChange={setSelectedChapters}\n              disabled={isRewriting}\n            />\n          </div>\n\n          {/* 右侧：模型配置 */}\n          <div className=\"lg:col-span-1\">\n            <ModelConfigSelector\n              selectedModel={selectedModel}\n              selectedConcurrency={selectedConcurrency}\n              onModelChange={setSelectedModel}\n              onConcurrencyChange={setSelectedConcurrency}\n              disabled={isRewriting}\n            />\n          </div>\n        </div>\n        )}\n      </div>\n\n      {/* Toast 通知 */}\n      {toast.show && (\n        <Toast\n          message={toast.message}\n          type={toast.type}\n          onClose={hideToast}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAbA;;;;;;;;;;;;AAgCe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAe;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAS;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAS;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAc,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAgB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,yKAAQ,EAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAS;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,yKAAQ,EAAS;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAa;QAAE,MAAM;QAAO,SAAS;QAAI,MAAM;IAAO;IAExF,MAAM,YAAY,CAAC,SAAiB;QAClC,SAAS;YAAE,MAAM;YAAM;YAAS;QAAK;IACvC;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;YAAO,SAAS;YAAI,MAAM;QAAO;IACpD;IAEA,MAAM,qBAAqB,OAAO;QAChC,MAAM,OAAO,OAAO;QACpB,IAAI,CAAC,MAAM;QAEX,MAAM,cAAc,OAAO,oBAAoB;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,YAAY;YACxB,OAAO;gBACL,UAAU,AAAC,SAAqB,OAAb,OAAO,KAAK,GAAI;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,UAAU,UAAU;QACtB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,cAAc;YACxD,UAAU,aAAa;YACvB;QACF;QAEA,eAAe;QAEf,IAAI;YACF,gBAAgB;YAChB,IAAI,gBAAgB;YACpB,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAA,OACnC,AAAC,GAAe,OAAb,KAAK,IAAI,EAAC,KAAe,OAAZ,KAAK,IAAI,EAAmD,OAAhD,KAAK,WAAW,GAAG,OAAO,KAAK,WAAW,GAAG,IAAG,MAC5E,IAAI,CAAC;gBACP,gBAAgB,AAAC,QAA2B,OAApB,eAAc,QAAmB,OAAb;YAC9C;YAEA,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,cAAc,EAAE;oBACzB,cAAc;oBACd,OAAO;oBACP,OAAO;oBACP,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YACxD;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,UAAU,OAAO,OAAO,EAAE;gBAC5B,gBAAgB,OAAO,IAAI,CAAC,KAAK;gBACjC,UAAU,WAAW;YACvB,OAAO;gBACL,UAAU,AAAC,SAAgC,OAAxB,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI,SAAU;gBAC9C,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,UAAU,kBAAkB;YAC5B,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB;QAC5B,eAAe;QACf,gBAAgB;QAChB,UAAU,SAAS;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,SAAS,IAAM,mBAAmB,CAAC;wCACnC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAO,OAAO;gDAAI,QAAQ;gDAAI,SAAQ;gDAAY,MAAK;gDAAO,QAAO;gDAAe,aAAY;;kEAC7G,6LAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,OAAM;wDAAK,QAAO;wDAAK,IAAG;wDAAI,IAAG;;;;;;kEACnD,6LAAC;wDAAK,GAAE;;;;;;;;;;;;4CACJ;;;;;;;kDAIR,6LAAC;wCACC,SAAS;wCACT,UAAU,eAAe,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;wCACjE,WAAU;wCACV,OACE,CAAC,gBAAgB,WACjB,CAAC,mBAAmB,YACpB,CAAC,eAAe,YAChB;kDAGD,cAAc,WAAW;;;;;;kDAE5B,6LAAC,0KAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAO,OAAO;gDAAI,QAAQ;gDAAI,SAAQ;gDAAY,MAAK;gDAAO,QAAO;gDAAe,aAAY;;kEAC7G,6LAAC;wDAAK,GAAE;;;;;;kEACR,6LAAC;wDAAK,GAAE;;;;;;kEACR,6LAAC;wDAAK,GAAE;;;;;;kEACR,6LAAC;wDAAK,GAAE;;;;;;;;;;;;4CACJ;;;;;;;kDAGR,6LAAC,0KAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAO,OAAO;gDAAI,QAAQ;gDAAI,SAAQ;gDAAY,MAAK;gDAAO,QAAO;gDAAe,aAAY;;kEAC7G,6LAAC;wDAAK,GAAE;;;;;;kEACR,6LAAC;wDAAS,QAAO;;;;;;kEACjB,6LAAC;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;;;;;;;;;;;;4CAC7B;;;;;;;kDAGR,6LAAC,0KAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,+OAAU;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;;;;;;oBAO9C,iCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+IAAW;4BACV,cAAc,gBAAgB;4BAC9B,eAAe;;;;;;;;;;;oBAMpB,eAAe,gBAAgB,CAAC,iCAC/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mJAAe;4BACd,OAAO;4BACP,YAAY;;;;;;;;;;;oBAKjB,CAAC,iCACA,6LAAC;wBAAI,WAAU;;0CAEf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8IAAU;oCACT,OAAO;oCACP,eAAe;oCACf,gBAAgB;oCAChB,UAAU;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAa;wCACZ,eAAe;wCACf,eAAe;wCACf,UAAU;;;;;;kDAEZ,6LAAC,oJAAgB;wCACf,OAAO,EAAE,0BAAA,oCAAA,cAAe,EAAE;wCAC1B,YAAY;wCACZ,oBAAoB;wCACpB,UAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mJAAe;oCACd,OAAO;oCACP,kBAAkB;oCAClB,kBAAkB;oCAClB,UAAU;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uJAAmB;oCAClB,eAAe;oCACf,qBAAqB;oCACrB,eAAe;oCACf,qBAAqB;oCACrB,UAAU;;;;;;;;;;;;;;;;;;;;;;;YAQjB,MAAM,IAAI,kBACT,6LAAC,yIAAK;gBACJ,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,SAAS;;;;;;;;;;;;AAKnB;GAlQwB;KAAA", "debugId": null}}]}