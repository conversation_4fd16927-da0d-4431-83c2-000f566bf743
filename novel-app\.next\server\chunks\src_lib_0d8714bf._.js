module.exports = [
"[project]/src/lib/context-utils.ts [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/src_lib_context-utils_ts_aa482aa6._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/context-utils.ts [app-route] (ecmascript)");
    });
});
}),
"[project]/src/lib/gemini.ts [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/src_lib_gemini_ts_7aaf7081._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/gemini.ts [app-route] (ecmascript)");
    });
});
}),
"[project]/src/lib/database.ts [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/lib/database.ts [app-route] (ecmascript)");
    });
});
}),
];