module.exports = [
"[project]/src/lib/context-utils.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "analyzeNovelClient",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["analyzeNovelClient"],
    "getChapterContext",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getChapterContext"],
    "getChapterContextClient",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getChapterContextClient"],
    "getChapterContextServer",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getChapterContextServer"],
    "getChapterContextWindow",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getChapterContextWindow"],
    "getChapterContextWindowClient",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getChapterContextWindowClient"],
    "getChapterContextWindowServer",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getChapterContextWindowServer"],
    "getNovelContext",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getNovelContext"],
    "getNovelContextClient",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getNovelContextClient"],
    "getNovelContextServer",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getNovelContextServer"],
    "rewriteChaptersWithContext",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["rewriteChaptersWithContext"],
    "rewriteTextWithContextClient",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rewriteTextWithContextClient"],
    "rewriteTextWithContextServer",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["rewriteTextWithContextServer"]
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/context-utils.ts [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/context-client.ts [app-route] (ecmascript)");
}),
];