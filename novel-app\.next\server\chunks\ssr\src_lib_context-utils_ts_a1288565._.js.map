{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/context-utils.ts"], "sourcesContent": ["// 上下文工具函数 - 服务端和客户端通用\nimport type { RewriteRequest, RewriteResponse } from './gemini';\n\n// 检查是否在服务端环境\nfunction isServerSide(): boolean {\n  return typeof window === 'undefined';\n}\n\n// 带上下文的重写函数（服务端专用）\nexport async function rewriteTextWithContextServer(\n  novelId: string,\n  chapterNumber: number,\n  originalText: string,\n  rules: string,\n  chapterTitle?: string,\n  model?: string\n): Promise<RewriteResponse> {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  try {\n    // 导入服务端模块\n    const { rewriteText } = await import('./gemini');\n    const { novelContextDb, chapterContextDb } = await import('./database');\n\n    // 获取小说整体上下文\n    const novelContext = novelContextDb.getByNovelId(novelId);\n\n    // 获取章节上下文\n    const chapterContext = chapterContextDb.getByChapter(novelId, chapterNumber);\n\n    // 构建请求\n    const request: RewriteRequest = {\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model,\n      novelContext: novelContext ? {\n        summary: novelContext.summary,\n        mainCharacters: novelContext.mainCharacters,\n        worldSetting: novelContext.worldSetting,\n        writingStyle: novelContext.writingStyle,\n        tone: novelContext.tone\n      } : undefined,\n      chapterContext: chapterContext ? {\n        previousChapterSummary: chapterContext.previousChapterSummary,\n        keyEvents: chapterContext.keyEvents,\n        characterStates: chapterContext.characterStates,\n        plotProgress: chapterContext.plotProgress,\n        contextualNotes: chapterContext.contextualNotes\n      } : undefined\n    };\n\n    return await rewriteText(request);\n  } catch (error) {\n    console.error('带上下文重写失败:', error);\n    // 如果获取上下文失败，回退到普通重写\n    const { rewriteText } = await import('./gemini');\n    return await rewriteText({\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model\n    });\n  }\n}\n\n// 获取小说上下文（服务端专用）\nexport async function getNovelContextServer(novelId: string) {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  const { novelContextDb } = await import('./database');\n  return novelContextDb.getByNovelId(novelId);\n}\n\n// 获取章节上下文（服务端专用）\nexport async function getChapterContextServer(novelId: string, chapterNumber: number) {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  const { chapterContextDb } = await import('./database');\n  return chapterContextDb.getByChapter(novelId, chapterNumber);\n}\n\n// 获取章节上下文窗口（服务端专用）\nexport async function getChapterContextWindowServer(\n  novelId: string,\n  chapterNumber: number,\n  windowSize: number = 2\n) {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  const { chapterContextDb } = await import('./database');\n  return chapterContextDb.getContextWindow(novelId, chapterNumber, windowSize);\n}\n\n// 带上下文的批量重写函数（服务端专用）\nexport async function rewriteChaptersWithContext(\n  novelId: string,\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number, details?: any) => void,\n  onChapterComplete?: (chapterIndex: number, result: any) => void,\n  concurrency: number = 3,\n  model: string = 'gemini-2.5-flash-lite',\n  enableFailureRecovery: boolean = true\n): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  try {\n    // 导入所需模块\n    const { novelContextDb, chapterContextDb } = await import('./database');\n    const { rewriteText } = await import('./gemini');\n\n    // 获取小说整体上下文\n    const novelContext = novelContextDb.getByNovelId(novelId);\n\n    const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);\n    let completed = 0;\n    let totalTokensUsed = 0;\n    const startTime = Date.now();\n\n    // 使用信号量控制并发\n    const semaphore = { count: concurrency, waiting: [] as Array<() => void> };\n\n    const acquire = () => new Promise<void>(resolve => {\n      if (semaphore.count > 0) {\n        semaphore.count--;\n        resolve();\n      } else {\n        semaphore.waiting.push(resolve);\n      }\n    });\n\n    const release = () => {\n      semaphore.count++;\n      if (semaphore.waiting.length > 0) {\n        const next = semaphore.waiting.shift();\n        if (next) {\n          semaphore.count--;\n          next();\n        }\n      }\n    };\n\n    const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {\n      await acquire();\n      const chapterStartTime = Date.now();\n\n      try {\n        // 获取章节上下文\n        const chapterContext = chapterContextDb.getByChapter(novelId, chapter.number);\n\n        // 构建带上下文的请求\n        const request: RewriteRequest = {\n          originalText: chapter.content,\n          rules,\n          chapterTitle: chapter.title,\n          chapterNumber: chapter.number,\n          model,\n          novelContext: novelContext ? {\n            summary: novelContext.summary,\n            mainCharacters: novelContext.mainCharacters,\n            worldSetting: novelContext.worldSetting,\n            writingStyle: novelContext.writingStyle,\n            tone: novelContext.tone\n          } : undefined,\n          chapterContext: chapterContext ? {\n            previousChapterSummary: chapterContext.previousChapterSummary,\n            keyEvents: chapterContext.keyEvents,\n            characterStates: chapterContext.characterStates,\n            plotProgress: chapterContext.plotProgress,\n            contextualNotes: chapterContext.contextualNotes\n          } : undefined\n        };\n\n        const result = await rewriteText(request);\n        const chapterProcessingTime = Date.now() - chapterStartTime;\n\n        if (result.tokensUsed) {\n          totalTokensUsed += result.tokensUsed;\n        }\n\n        const chapterResult = {\n          success: result.success,\n          content: result.rewrittenText,\n          error: result.error,\n          details: {\n            apiKeyUsed: result.apiKeyUsed,\n            tokensUsed: result.tokensUsed,\n            model: result.model,\n            processingTime: chapterProcessingTime,\n            chapterNumber: chapter.number,\n            chapterTitle: chapter.title,\n            hasContext: !!(novelContext || chapterContext),\n            detailedError: result.detailedError, // 传递详细错误信息\n            debugInfo: result.debugInfo, // 传递调试信息\n          }\n        };\n\n        results[index] = chapterResult;\n        completed++;\n\n        // 实时回调章节完成\n        if (onChapterComplete) {\n          onChapterComplete(index, chapterResult);\n        }\n\n        // 更新进度\n        const progress = (completed / chapters.length) * 100;\n        const totalTime = Date.now() - startTime;\n        const averageTimePerChapter = totalTime / completed;\n\n        if (onProgress) {\n          onProgress(progress, chapter.number, {\n            completed,\n            totalTokensUsed,\n            totalTime,\n            averageTimePerChapter,\n            hasContext: !!(novelContext || chapterContext)\n          });\n        }\n\n        console.log(`第 ${chapter.number} 章重写${result.success ? '成功' : '失败'}${novelContext || chapterContext ? '（使用上下文）' : ''}: ${result.error || '完成'}`);\n\n      } catch (error) {\n        const chapterProcessingTime = Date.now() - chapterStartTime;\n        const chapterResult = {\n          success: false,\n          content: '',\n          error: `重写异常: ${error instanceof Error ? error.message : '未知错误'}`,\n          details: {\n            processingTime: chapterProcessingTime,\n            chapterNumber: chapter.number,\n            chapterTitle: chapter.title,\n            hasContext: !!(novelContext || chapterContext)\n          }\n        };\n\n        results[index] = chapterResult;\n        completed++;\n\n        if (onChapterComplete) {\n          onChapterComplete(index, chapterResult);\n        }\n\n        console.error(`第 ${chapter.number} 章重写异常:`, error);\n      } finally {\n        release();\n      }\n    };\n\n    // 并行处理所有章节\n    const promises = chapters.map((chapter, index) => processChapter(chapter, index));\n    await Promise.all(promises);\n\n    return results;\n\n  } catch (error) {\n    console.error('批量重写失败，回退到普通重写:', error);\n    // 如果上下文重写失败，回退到普通重写\n    const { rewriteChapters } = await import('./gemini');\n    return await rewriteChapters(\n      chapters,\n      rules,\n      onProgress,\n      onChapterComplete,\n      concurrency,\n      model,\n      enableFailureRecovery\n    );\n  }\n}\n\n// 重新导出客户端函数，提供统一接口\nexport {\n  getNovelContext,\n  getChapterContext,\n  getChapterContextWindow,\n  getNovelContextClient,\n  getChapterContextClient,\n  getChapterContextWindowClient,\n  analyzeNovelClient,\n  rewriteTextWithContextClient\n} from './context-client';\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;AA4RtB,mBAAmB;AACnB;AA1RA,aAAa;AACb,SAAS;IACP,OAAO,gBAAkB;AAC3B;AAGO,eAAe,6BACpB,OAAe,EACf,aAAqB,EACrB,YAAoB,EACpB,KAAa,EACb,YAAqB,EACrB,KAAc;IAEd,IAAI,CAAC;;IAIL,IAAI;QACF,UAAU;QACV,MAAM,EAAE,WAAW,EAAE,GAAG;QACxB,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG;QAE7C,YAAY;QACZ,MAAM,eAAe,eAAe,YAAY,CAAC;QAEjD,UAAU;QACV,MAAM,kBAAiB,iBAAiB,YAAY,CAAC,SAAS;QAE9D,OAAO;QACP,MAAM,UAA0B;YAC9B;YACA;YACA;YACA;YACA;YACA,cAAc,eAAe;gBAC3B,SAAS,aAAa,OAAO;gBAC7B,gBAAgB,aAAa,cAAc;gBAC3C,cAAc,aAAa,YAAY;gBACvC,cAAc,aAAa,YAAY;gBACvC,MAAM,aAAa,IAAI;YACzB,IAAI;YACJ,gBAAgB,kBAAiB;gBAC/B,wBAAwB,gBAAe,sBAAsB;gBAC7D,WAAW,gBAAe,SAAS;gBACnC,iBAAiB,gBAAe,eAAe;gBAC/C,cAAc,gBAAe,YAAY;gBACzC,iBAAiB,gBAAe,eAAe;YACjD,IAAI;QACN;QAEA,OAAO,MAAM,YAAY;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,oBAAoB;QACpB,MAAM,EAAE,WAAW,EAAE,GAAG;QACxB,OAAO,MAAM,YAAY;YACvB;YACA;YACA;YACA;YACA;QACF;IACF;AACF;AAGO,eAAe,sBAAsB,OAAe;IACzD,IAAI,CAAC;;IAIL,MAAM,EAAE,cAAc,EAAE,GAAG;IAC3B,OAAO,eAAe,YAAY,CAAC;AACrC;AAGO,eAAe,wBAAwB,OAAe,EAAE,aAAqB;IAClF,IAAI,CAAC;;IAIL,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,OAAO,iBAAiB,YAAY,CAAC,SAAS;AAChD;AAGO,eAAe,8BACpB,OAAe,EACf,aAAqB,EACrB,aAAqB,CAAC;IAEtB,IAAI,CAAC;;IAIL,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,OAAO,iBAAiB,gBAAgB,CAAC,SAAS,eAAe;AACnE;AAGO,eAAe,2BACpB,OAAe,EACf,QAAmE,EACnE,KAAa,EACb,UAA8E,EAC9E,iBAA+D,EAC/D,cAAsB,CAAC,EACvB,QAAgB,uBAAuB,EACvC,wBAAiC,IAAI;IAErC,IAAI,CAAC;;IAIL,IAAI;QACF,SAAS;QACT,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG;QAC7C,MAAM,EAAE,WAAW,EAAE,GAAG;QAExB,YAAY;QACZ,MAAM,eAAe,eAAe,YAAY,CAAC;QAEjD,MAAM,UAAuF,IAAI,MAAM,SAAS,MAAM;QACtH,IAAI,YAAY;QAChB,IAAI,kBAAkB;QACtB,MAAM,YAAY,KAAK,GAAG;QAE1B,YAAY;QACZ,MAAM,YAAY;YAAE,OAAO;YAAa,SAAS,EAAE;QAAsB;QAEzE,MAAM,UAAU,IAAM,IAAI,QAAc,CAAA;gBACtC,IAAI,UAAU,KAAK,GAAG,GAAG;oBACvB,UAAU,KAAK;oBACf;gBACF,OAAO;oBACL,UAAU,OAAO,CAAC,IAAI,CAAC;gBACzB;YACF;QAEA,MAAM,UAAU;YACd,UAAU,KAAK;YACf,IAAI,UAAU,OAAO,CAAC,MAAM,GAAG,GAAG;gBAChC,MAAM,OAAO,UAAU,OAAO,CAAC,KAAK;gBACpC,IAAI,MAAM;oBACR,UAAU,KAAK;oBACf;gBACF;YACF;QACF;QAEA,MAAM,iBAAiB,OAAO,SAA6D;YACzF,MAAM;YACN,MAAM,mBAAmB,KAAK,GAAG;YAEjC,IAAI;gBACF,UAAU;gBACV,MAAM,kBAAiB,iBAAiB,YAAY,CAAC,SAAS,QAAQ,MAAM;gBAE5E,YAAY;gBACZ,MAAM,UAA0B;oBAC9B,cAAc,QAAQ,OAAO;oBAC7B;oBACA,cAAc,QAAQ,KAAK;oBAC3B,eAAe,QAAQ,MAAM;oBAC7B;oBACA,cAAc,eAAe;wBAC3B,SAAS,aAAa,OAAO;wBAC7B,gBAAgB,aAAa,cAAc;wBAC3C,cAAc,aAAa,YAAY;wBACvC,cAAc,aAAa,YAAY;wBACvC,MAAM,aAAa,IAAI;oBACzB,IAAI;oBACJ,gBAAgB,kBAAiB;wBAC/B,wBAAwB,gBAAe,sBAAsB;wBAC7D,WAAW,gBAAe,SAAS;wBACnC,iBAAiB,gBAAe,eAAe;wBAC/C,cAAc,gBAAe,YAAY;wBACzC,iBAAiB,gBAAe,eAAe;oBACjD,IAAI;gBACN;gBAEA,MAAM,SAAS,MAAM,YAAY;gBACjC,MAAM,wBAAwB,KAAK,GAAG,KAAK;gBAE3C,IAAI,OAAO,UAAU,EAAE;oBACrB,mBAAmB,OAAO,UAAU;gBACtC;gBAEA,MAAM,gBAAgB;oBACpB,SAAS,OAAO,OAAO;oBACvB,SAAS,OAAO,aAAa;oBAC7B,OAAO,OAAO,KAAK;oBACnB,SAAS;wBACP,YAAY,OAAO,UAAU;wBAC7B,YAAY,OAAO,UAAU;wBAC7B,OAAO,OAAO,KAAK;wBACnB,gBAAgB;wBAChB,eAAe,QAAQ,MAAM;wBAC7B,cAAc,QAAQ,KAAK;wBAC3B,YAAY,CAAC,CAAC,CAAC,gBAAgB,eAAc;wBAC7C,eAAe,OAAO,aAAa;wBACnC,WAAW,OAAO,SAAS;oBAC7B;gBACF;gBAEA,OAAO,CAAC,MAAM,GAAG;gBACjB;gBAEA,WAAW;gBACX,IAAI,mBAAmB;oBACrB,kBAAkB,OAAO;gBAC3B;gBAEA,OAAO;gBACP,MAAM,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI;gBACjD,MAAM,YAAY,KAAK,GAAG,KAAK;gBAC/B,MAAM,wBAAwB,YAAY;gBAE1C,IAAI,YAAY;oBACd,WAAW,UAAU,QAAQ,MAAM,EAAE;wBACnC;wBACA;wBACA;wBACA;wBACA,YAAY,CAAC,CAAC,CAAC,gBAAgB,eAAc;oBAC/C;gBACF;gBAEA,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,OAAO,gBAAgB,kBAAiB,YAAY,GAAG,EAAE,EAAE,OAAO,KAAK,IAAI,MAAM;YAEjJ,EAAE,OAAO,OAAO;gBACd,MAAM,wBAAwB,KAAK,GAAG,KAAK;gBAC3C,MAAM,gBAAgB;oBACpB,SAAS;oBACT,SAAS;oBACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;oBACjE,SAAS;wBACP,gBAAgB;wBAChB,eAAe,QAAQ,MAAM;wBAC7B,cAAc,QAAQ,KAAK;wBAC3B,YAAY,CAAC,CAAC,CAAC,gBAAgB,cAAc;oBAC/C;gBACF;gBAEA,OAAO,CAAC,MAAM,GAAG;gBACjB;gBAEA,IAAI,mBAAmB;oBACrB,kBAAkB,OAAO;gBAC3B;gBAEA,QAAQ,KAAK,CAAC,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC,EAAE;YAC9C,SAAU;gBACR;YACF;QACF;QAEA,WAAW;QACX,MAAM,WAAW,SAAS,GAAG,CAAC,CAAC,SAAS,QAAU,eAAe,SAAS;QAC1E,MAAM,QAAQ,GAAG,CAAC;QAElB,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,oBAAoB;QACpB,MAAM,EAAE,eAAe,EAAE,GAAG;QAC5B,OAAO,MAAM,gBACX,UACA,OACA,YACA,mBACA,aACA,OACA;IAEJ;AACF", "debugId": null}}]}