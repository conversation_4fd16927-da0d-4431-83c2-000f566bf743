module.exports = [
"[project]/.next-internal/server/app/api/rewrite/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[project]/src/lib/database.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "chapterContextDb",
    ()=>chapterContextDb,
    "chapterDb",
    ()=>chapterDb,
    "characterDb",
    ()=>characterDb,
    "jobDb",
    ()=>jobDb,
    "novelContextDb",
    ()=>novelContextDb,
    "novelDb",
    ()=>novelDb,
    "presetDb",
    ()=>presetDb,
    "ruleDb",
    ()=>ruleDb
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
;
;
// 数据存储路径
const DATA_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const NOVELS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'novels.json');
const CHAPTERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'chapters.json');
const RULES_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'rewrite_rules.json');
const JOBS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'rewrite_jobs.json');
const CHARACTERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'characters.json');
const PRESETS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'presets.json');
const NOVEL_CONTEXTS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'novel-contexts.json');
const CHAPTER_CONTEXTS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'chapter-contexts.json');
// 确保数据目录存在
function ensureDataDir() {
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DATA_DIR)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DATA_DIR, {
            recursive: true
        });
    }
}
// 读取JSON文件
function readJsonFile(filePath) {
    ensureDataDir();
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath)) {
        return [];
    }
    try {
        const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`Error reading ${filePath}:`, error);
        return [];
    }
}
// 写入JSON文件
function writeJsonFile(filePath, data) {
    ensureDataDir();
    try {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
    } catch (error) {
        console.error(`Error writing ${filePath}:`, error);
        throw error;
    }
}
// 生成唯一ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
}
// 基于内容生成确定性ID
function generateDeterministicId(content) {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHash('md5').update(content).digest('hex').substring(0, 18);
}
const novelDb = {
    getAll: ()=>readJsonFile(NOVELS_FILE),
    getById: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        return novels.find((novel)=>novel.id === id);
    },
    create: (novel)=>{
        const novels = readJsonFile(NOVELS_FILE);
        // 使用书名生成确定性ID
        const novelId = generateDeterministicId(novel.title);
        // 检查是否已存在相同ID的小说
        const existingNovel = novels.find((n)=>n.id === novelId);
        if (existingNovel) {
            // 如果已存在，更新现有记录
            existingNovel.filename = novel.filename;
            existingNovel.chapterCount = novel.chapterCount;
            writeJsonFile(NOVELS_FILE, novels);
            return existingNovel;
        }
        const newNovel = {
            ...novel,
            id: novelId,
            createdAt: new Date().toISOString()
        };
        novels.push(newNovel);
        writeJsonFile(NOVELS_FILE, novels);
        return newNovel;
    },
    update: (id, updates)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return null;
        novels[index] = {
            ...novels[index],
            ...updates
        };
        writeJsonFile(NOVELS_FILE, novels);
        return novels[index];
    },
    delete: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return false;
        novels.splice(index, 1);
        writeJsonFile(NOVELS_FILE, novels);
        return true;
    }
};
const chapterDb = {
    getAll: ()=>readJsonFile(CHAPTERS_FILE),
    getByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.filter((chapter)=>chapter.novelId === novelId);
    },
    getById: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.find((chapter)=>chapter.id === id);
    },
    create: (chapter)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const newChapter = {
            ...chapter,
            id: generateId(),
            createdAt: new Date().toISOString()
        };
        chapters.push(newChapter);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return newChapter;
    },
    createBatch: (chapters)=>{
        const existingChapters = readJsonFile(CHAPTERS_FILE);
        const newChapters = chapters.map((chapter)=>({
                ...chapter,
                id: generateId(),
                createdAt: new Date().toISOString()
            }));
        existingChapters.push(...newChapters);
        writeJsonFile(CHAPTERS_FILE, existingChapters);
        return newChapters;
    },
    delete: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const index = chapters.findIndex((chapter)=>chapter.id === id);
        if (index === -1) return false;
        chapters.splice(index, 1);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const filteredChapters = chapters.filter((chapter)=>chapter.novelId !== novelId);
        writeJsonFile(CHAPTERS_FILE, filteredChapters);
        return true;
    }
};
const ruleDb = {
    getAll: ()=>readJsonFile(RULES_FILE),
    getById: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        return rules.find((rule)=>rule.id === id);
    },
    create: (rule)=>{
        const rules = readJsonFile(RULES_FILE);
        const newRule = {
            ...rule,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        rules.push(newRule);
        writeJsonFile(RULES_FILE, rules);
        return newRule;
    },
    update: (id, updates)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return null;
        rules[index] = {
            ...rules[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(RULES_FILE, rules);
        return rules[index];
    },
    delete: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return false;
        rules.splice(index, 1);
        writeJsonFile(RULES_FILE, rules);
        return true;
    }
};
const jobDb = {
    getAll: ()=>readJsonFile(JOBS_FILE),
    getById: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        return jobs.find((job)=>job.id === id);
    },
    create: (job)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const newJob = {
            ...job,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        jobs.push(newJob);
        writeJsonFile(JOBS_FILE, jobs);
        return newJob;
    },
    update: (id, updates)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return null;
        jobs[index] = {
            ...jobs[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(JOBS_FILE, jobs);
        return jobs[index];
    },
    delete: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return false;
        jobs.splice(index, 1);
        writeJsonFile(JOBS_FILE, jobs);
        return true;
    }
};
const characterDb = {
    getAll: ()=>readJsonFile(CHARACTERS_FILE),
    getByNovelId: (novelId)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        return characters.filter((character)=>character.novelId === novelId);
    },
    getById: (id)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        return characters.find((character)=>character.id === id);
    },
    create: (character)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const newCharacter = {
            ...character,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        characters.push(newCharacter);
        writeJsonFile(CHARACTERS_FILE, characters);
        return newCharacter;
    },
    update: (id, updates)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const index = characters.findIndex((character)=>character.id === id);
        if (index === -1) return null;
        characters[index] = {
            ...characters[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(CHARACTERS_FILE, characters);
        return characters[index];
    },
    delete: (id)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const index = characters.findIndex((character)=>character.id === id);
        if (index === -1) return false;
        characters.splice(index, 1);
        writeJsonFile(CHARACTERS_FILE, characters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const filteredCharacters = characters.filter((character)=>character.novelId !== novelId);
        writeJsonFile(CHARACTERS_FILE, filteredCharacters);
        return true;
    }
};
const presetDb = {
    getAll: ()=>readJsonFile(PRESETS_FILE),
    getById: (id)=>{
        const presets = readJsonFile(PRESETS_FILE);
        return presets.find((preset)=>preset.id === id);
    },
    create: (preset)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const newPreset = {
            ...preset,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        presets.push(newPreset);
        writeJsonFile(PRESETS_FILE, presets);
        return newPreset;
    },
    update: (id, updates)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const index = presets.findIndex((preset)=>preset.id === id);
        if (index === -1) return null;
        presets[index] = {
            ...presets[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(PRESETS_FILE, presets);
        return presets[index];
    },
    delete: (id)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const index = presets.findIndex((preset)=>preset.id === id);
        if (index === -1) return false;
        presets.splice(index, 1);
        writeJsonFile(PRESETS_FILE, presets);
        return true;
    }
};
const novelContextDb = {
    getAll: ()=>readJsonFile(NOVEL_CONTEXTS_FILE),
    getByNovelId: (novelId)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        return contexts.find((context)=>context.novelId === novelId);
    },
    getById: (id)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        return contexts.find((context)=>context.id === id);
    },
    create: (context)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        const newContext = {
            ...context,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        contexts.push(newContext);
        writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
        return newContext;
    },
    update: (id, updates)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return undefined;
        contexts[index] = {
            ...contexts[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
        return contexts[index];
    },
    delete: (id)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return false;
        contexts.splice(index, 1);
        writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
        return true;
    }
};
const chapterContextDb = {
    getAll: ()=>readJsonFile(CHAPTER_CONTEXTS_FILE),
    getByNovelId: (novelId)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        return contexts.filter((context)=>context.novelId === novelId);
    },
    getByChapter: (novelId, chapterNumber)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        return contexts.find((context)=>context.novelId === novelId && context.chapterNumber === chapterNumber);
    },
    getById: (id)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        return contexts.find((context)=>context.id === id);
    },
    create: (context)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const newContext = {
            ...context,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        contexts.push(newContext);
        writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
        return newContext;
    },
    update: (id, updates)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return undefined;
        contexts[index] = {
            ...contexts[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
        return contexts[index];
    },
    delete: (id)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return false;
        contexts.splice(index, 1);
        writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
        return true;
    },
    // 获取章节的上下文窗口（前后几章的上下文）
    getContextWindow: (novelId, chapterNumber, windowSize = 2)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const novelContexts = contexts.filter((context)=>context.novelId === novelId);
        const startChapter = Math.max(1, chapterNumber - windowSize);
        const endChapter = chapterNumber + windowSize;
        return novelContexts.filter((context)=>context.chapterNumber >= startChapter && context.chapterNumber <= endChapter).sort((a, b)=>a.chapterNumber - b.chapterNumber);
    }
};
}),
"[project]/src/lib/context-client.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// 客户端上下文工具函数
__turbopack_context__.s([
    "analyzeNovelClient",
    ()=>analyzeNovelClient,
    "getChapterContext",
    ()=>getChapterContext,
    "getChapterContextClient",
    ()=>getChapterContextClient,
    "getChapterContextWindow",
    ()=>getChapterContextWindow,
    "getChapterContextWindowClient",
    ()=>getChapterContextWindowClient,
    "getNovelContext",
    ()=>getNovelContext,
    "getNovelContextClient",
    ()=>getNovelContextClient,
    "rewriteTextWithContextClient",
    ()=>rewriteTextWithContextClient
]);
// 检查是否在客户端环境
function isClientSide() {
    return "undefined" !== 'undefined';
}
async function getNovelContextClient(novelId) {
    if (!isClientSide()) {
        throw new Error('This function can only be used on client side');
    }
    try {
        const response = await fetch(`/api/context/novel?novelId=${novelId}`);
        const result = await response.json();
        if (!result.success) {
            console.warn('获取小说上下文失败:', result.error);
            return null;
        }
        return result.data;
    } catch (error) {
        console.error('获取小说上下文失败:', error);
        return null;
    }
}
async function getChapterContextClient(novelId, chapterNumber) {
    if (!isClientSide()) {
        throw new Error('This function can only be used on client side');
    }
    try {
        const response = await fetch(`/api/context/chapter?novelId=${novelId}&chapterNumber=${chapterNumber}`);
        const result = await response.json();
        if (!result.success) {
            console.warn('获取章节上下文失败:', result.error);
            return null;
        }
        return result.data;
    } catch (error) {
        console.error('获取章节上下文失败:', error);
        return null;
    }
}
async function getChapterContextWindowClient(novelId, chapterNumber, windowSize = 2) {
    if (!isClientSide()) {
        throw new Error('This function can only be used on client side');
    }
    try {
        const response = await fetch(`/api/context/window?novelId=${novelId}&chapterNumber=${chapterNumber}&windowSize=${windowSize}`);
        const result = await response.json();
        if (!result.success) {
            console.warn('获取章节上下文窗口失败:', result.error);
            return [];
        }
        return result.data.contexts;
    } catch (error) {
        console.error('获取章节上下文窗口失败:', error);
        return [];
    }
}
async function analyzeNovelClient(novelId, analyzeChapters = false) {
    if (!isClientSide()) {
        throw new Error('This function can only be used on client side');
    }
    const response = await fetch('/api/context/analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            novelId: novelId,
            analyzeChapters: analyzeChapters
        })
    });
    const result = await response.json();
    if (!result.success) {
        throw new Error(result.error || '分析失败');
    }
    return {
        novelContext: result.data.novelContext,
        chapterContexts: result.data.chapterContexts
    };
}
async function rewriteTextWithContextClient(novelId, chapterNumbers, rules, model, onProgress) {
    if (!isClientSide()) {
        throw new Error('This function can only be used on client side');
    }
    const response = await fetch('/api/rewrite', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            novelId: novelId,
            chapterNumbers: chapterNumbers,
            rules: rules,
            model: model || 'gemini-2.5-flash-lite'
        })
    });
    const result = await response.json();
    if (!result.success) {
        throw new Error(result.error || '重写失败');
    }
    return result.data.results;
}
async function getNovelContext(novelId) {
    if (isClientSide()) //TURBOPACK unreachable
    ;
    else {
        // 服务端环境，动态导入服务端函数
        const { getNovelContextServer } = await __turbopack_context__.A("[project]/src/lib/context-utils.ts [app-route] (ecmascript, async loader)");
        return getNovelContextServer(novelId);
    }
}
async function getChapterContext(novelId, chapterNumber) {
    if (isClientSide()) //TURBOPACK unreachable
    ;
    else {
        // 服务端环境，动态导入服务端函数
        const { getChapterContextServer } = await __turbopack_context__.A("[project]/src/lib/context-utils.ts [app-route] (ecmascript, async loader)");
        return getChapterContextServer(novelId, chapterNumber);
    }
}
async function getChapterContextWindow(novelId, chapterNumber, windowSize = 2) {
    if (isClientSide()) //TURBOPACK unreachable
    ;
    else {
        // 服务端环境，动态导入服务端函数
        const { getChapterContextWindowServer } = await __turbopack_context__.A("[project]/src/lib/context-utils.ts [app-route] (ecmascript, async loader)");
        return getChapterContextWindowServer(novelId, chapterNumber, windowSize);
    }
}
}),
"[project]/src/lib/context-utils.ts [app-route] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

// 上下文工具函数 - 服务端和客户端通用
__turbopack_context__.s([
    "getChapterContextServer",
    ()=>getChapterContextServer,
    "getChapterContextWindowServer",
    ()=>getChapterContextWindowServer,
    "getNovelContextServer",
    ()=>getNovelContextServer,
    "rewriteChaptersWithContext",
    ()=>rewriteChaptersWithContext,
    "rewriteTextWithContextServer",
    ()=>rewriteTextWithContextServer
]);
// 重新导出客户端函数，提供统一接口
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/context-client.ts [app-route] (ecmascript)");
// 检查是否在服务端环境
function isServerSide() {
    return "undefined" === 'undefined';
}
async function rewriteTextWithContextServer(novelId, chapterNumber, originalText, rules, chapterTitle, model) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    try {
        // 导入服务端模块
        const { rewriteText } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-route] (ecmascript, async loader)");
        const { novelContextDb, chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)");
        // 获取小说整体上下文
        const novelContext = novelContextDb.getByNovelId(novelId);
        // 获取章节上下文
        const chapterContext1 = chapterContextDb.getByChapter(novelId, chapterNumber);
        // 构建请求
        const request = {
            originalText,
            rules,
            chapterTitle,
            chapterNumber,
            model,
            novelContext: novelContext ? {
                summary: novelContext.summary,
                mainCharacters: novelContext.mainCharacters,
                worldSetting: novelContext.worldSetting,
                writingStyle: novelContext.writingStyle,
                tone: novelContext.tone
            } : undefined,
            chapterContext: chapterContext1 ? {
                previousChapterSummary: chapterContext1.previousChapterSummary,
                keyEvents: chapterContext1.keyEvents,
                characterStates: chapterContext1.characterStates,
                plotProgress: chapterContext1.plotProgress,
                contextualNotes: chapterContext1.contextualNotes
            } : undefined
        };
        return await rewriteText(request);
    } catch (error) {
        console.error('带上下文重写失败:', error);
        // 如果获取上下文失败，回退到普通重写
        const { rewriteText } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-route] (ecmascript, async loader)");
        return await rewriteText({
            originalText,
            rules,
            chapterTitle,
            chapterNumber,
            model
        });
    }
}
async function getNovelContextServer(novelId) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    const { novelContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)");
    return novelContextDb.getByNovelId(novelId);
}
async function getChapterContextServer(novelId, chapterNumber) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    const { chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)");
    return chapterContextDb.getByChapter(novelId, chapterNumber);
}
async function getChapterContextWindowServer(novelId, chapterNumber, windowSize = 2) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    const { chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)");
    return chapterContextDb.getContextWindow(novelId, chapterNumber, windowSize);
}
async function rewriteChaptersWithContext(novelId, chapters, rules, onProgress, onChapterComplete, concurrency = 3, model = 'gemini-2.5-flash-lite', enableFailureRecovery = true) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    try {
        // 导入所需模块
        const { novelContextDb, chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)");
        const { rewriteText } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-route] (ecmascript, async loader)");
        // 获取小说整体上下文
        const novelContext = novelContextDb.getByNovelId(novelId);
        const results = new Array(chapters.length);
        let completed = 0;
        let totalTokensUsed = 0;
        const startTime = Date.now();
        // 使用信号量控制并发
        const semaphore = {
            count: concurrency,
            waiting: []
        };
        const acquire = ()=>new Promise((resolve)=>{
                if (semaphore.count > 0) {
                    semaphore.count--;
                    resolve();
                } else {
                    semaphore.waiting.push(resolve);
                }
            });
        const release = ()=>{
            semaphore.count++;
            if (semaphore.waiting.length > 0) {
                const next = semaphore.waiting.shift();
                if (next) {
                    semaphore.count--;
                    next();
                }
            }
        };
        const processChapter = async (chapter, index)=>{
            await acquire();
            const chapterStartTime = Date.now();
            try {
                // 获取章节上下文
                const chapterContext1 = chapterContextDb.getByChapter(novelId, chapter.number);
                // 构建带上下文的请求
                const request = {
                    originalText: chapter.content,
                    rules,
                    chapterTitle: chapter.title,
                    chapterNumber: chapter.number,
                    model,
                    novelContext: novelContext ? {
                        summary: novelContext.summary,
                        mainCharacters: novelContext.mainCharacters,
                        worldSetting: novelContext.worldSetting,
                        writingStyle: novelContext.writingStyle,
                        tone: novelContext.tone
                    } : undefined,
                    chapterContext: chapterContext1 ? {
                        previousChapterSummary: chapterContext1.previousChapterSummary,
                        keyEvents: chapterContext1.keyEvents,
                        characterStates: chapterContext1.characterStates,
                        plotProgress: chapterContext1.plotProgress,
                        contextualNotes: chapterContext1.contextualNotes
                    } : undefined
                };
                const result = await rewriteText(request);
                const chapterProcessingTime = Date.now() - chapterStartTime;
                if (result.tokensUsed) {
                    totalTokensUsed += result.tokensUsed;
                }
                const chapterResult = {
                    success: result.success,
                    content: result.rewrittenText,
                    error: result.error,
                    details: {
                        apiKeyUsed: result.apiKeyUsed,
                        tokensUsed: result.tokensUsed,
                        model: result.model,
                        processingTime: chapterProcessingTime,
                        chapterNumber: chapter.number,
                        chapterTitle: chapter.title,
                        hasContext: !!(novelContext || chapterContext1)
                    }
                };
                results[index] = chapterResult;
                completed++;
                // 实时回调章节完成
                if (onChapterComplete) {
                    onChapterComplete(index, chapterResult);
                }
                // 更新进度
                const progress = completed / chapters.length * 100;
                const totalTime = Date.now() - startTime;
                const averageTimePerChapter = totalTime / completed;
                if (onProgress) {
                    onProgress(progress, chapter.number, {
                        completed,
                        totalTokensUsed,
                        totalTime,
                        averageTimePerChapter,
                        hasContext: !!(novelContext || chapterContext1)
                    });
                }
                console.log(`第 ${chapter.number} 章重写${result.success ? '成功' : '失败'}${novelContext || chapterContext1 ? '（使用上下文）' : ''}: ${result.error || '完成'}`);
            } catch (error) {
                const chapterProcessingTime = Date.now() - chapterStartTime;
                const chapterResult = {
                    success: false,
                    content: '',
                    error: `重写异常: ${error instanceof Error ? error.message : '未知错误'}`,
                    details: {
                        processingTime: chapterProcessingTime,
                        chapterNumber: chapter.number,
                        chapterTitle: chapter.title,
                        hasContext: !!(novelContext || chapterContext)
                    }
                };
                results[index] = chapterResult;
                completed++;
                if (onChapterComplete) {
                    onChapterComplete(index, chapterResult);
                }
                console.error(`第 ${chapter.number} 章重写异常:`, error);
            } finally{
                release();
            }
        };
        // 并行处理所有章节
        const promises = chapters.map((chapter, index)=>processChapter(chapter, index));
        await Promise.all(promises);
        return results;
    } catch (error) {
        console.error('批量重写失败，回退到普通重写:', error);
        // 如果上下文重写失败，回退到普通重写
        const { rewriteChapters } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-route] (ecmascript, async loader)");
        return await rewriteChapters(chapters, rules, onProgress, onChapterComplete, concurrency, model, enableFailureRecovery);
    }
}
;
}),
"[project]/src/lib/file-manager.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FileManager",
    ()=>FileManager,
    "fileManager",
    ()=>fileManager
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
class FileManager {
    static instance;
    baseDir;
    constructor(){
        this.baseDir = process.cwd();
    }
    static getInstance() {
        if (!FileManager.instance) {
            FileManager.instance = new FileManager();
        }
        return FileManager.instance;
    }
    // 确保目录存在
    ensureDir(dirPath) {
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(dirPath, {
                recursive: true
            });
        }
    }
    // 获取novels目录路径
    getNovelsDir() {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, '..', 'novels');
    }
    // 获取chapters目录路径
    getChaptersDir() {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, '..', 'chapters');
    }
    // 获取数据目录路径
    getDataDir() {
        const dataDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, 'data');
        this.ensureDir(dataDir);
        return dataDir;
    }
    // 获取改写结果目录路径
    getRewrittenDir() {
        const rewrittenDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.getDataDir(), 'rewritten');
        this.ensureDir(rewrittenDir);
        return rewrittenDir;
    }
    // 获取特定小说的改写结果目录
    getNovelRewrittenDir(novelTitle) {
        const novelDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.getRewrittenDir(), this.sanitizeFilename(novelTitle));
        this.ensureDir(novelDir);
        return novelDir;
    }
    // 获取完成小说目录路径
    getDoneNovelsDir() {
        const doneNovelsDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, '..', 'done-novels');
        this.ensureDir(doneNovelsDir);
        return doneNovelsDir;
    }
    // 获取特定小说的章节目录
    getNovelChaptersDir(novelTitle) {
        const chaptersDir = this.getChaptersDir();
        this.ensureDir(chaptersDir);
        const novelDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(chaptersDir, this.sanitizeFilename(novelTitle));
        this.ensureDir(novelDir);
        return novelDir;
    }
    // 清理文件名中的非法字符
    sanitizeFilename(filename) {
        return filename.replace(/[<>:"/\\|?*]/g, '_').trim();
    }
    // 读取文件内容
    readFile(filePath) {
        try {
            return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
        } catch (error) {
            console.error(`读取文件失败: ${filePath}`, error);
            throw error;
        }
    }
    // 写入文件内容
    writeFile(filePath, content) {
        try {
            const dir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(filePath);
            this.ensureDir(dir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, content, 'utf-8');
        } catch (error) {
            console.error(`写入文件失败: ${filePath}`, error);
            throw error;
        }
    }
    // 检查文件是否存在
    fileExists(filePath) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath);
    }
    // 获取目录中的所有文件
    listFiles(dirPath, extensions) {
        try {
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                return [];
            }
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(dirPath);
            if (extensions) {
                return files.filter((file)=>{
                    const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(file).toLowerCase();
                    return extensions.includes(ext);
                });
            }
            return files;
        } catch (error) {
            console.error(`读取目录失败: ${dirPath}`, error);
            return [];
        }
    }
    // 获取文件信息
    getFileStats(filePath) {
        try {
            return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filePath);
        } catch (error) {
            console.error(`获取文件信息失败: ${filePath}`, error);
            return null;
        }
    }
    // 删除文件
    deleteFile(filePath) {
        try {
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(filePath);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`删除文件失败: ${filePath}`, error);
            return false;
        }
    }
    // 删除目录
    deleteDir(dirPath) {
        try {
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].rmSync(dirPath, {
                    recursive: true,
                    force: true
                });
                return true;
            }
            return false;
        } catch (error) {
            console.error(`删除目录失败: ${dirPath}`, error);
            return false;
        }
    }
    // 复制文件
    copyFile(srcPath, destPath) {
        try {
            const destDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(destPath);
            this.ensureDir(destDir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].copyFileSync(srcPath, destPath);
            return true;
        } catch (error) {
            console.error(`复制文件失败: ${srcPath} -> ${destPath}`, error);
            return false;
        }
    }
    // 移动文件
    moveFile(srcPath, destPath) {
        try {
            const destDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(destPath);
            this.ensureDir(destDir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].renameSync(srcPath, destPath);
            return true;
        } catch (error) {
            console.error(`移动文件失败: ${srcPath} -> ${destPath}`, error);
            return false;
        }
    }
    // 获取目录大小
    getDirSize(dirPath) {
        let totalSize = 0;
        try {
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                return 0;
            }
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(dirPath);
            for (const file of files){
                const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file);
                const stats = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filePath);
                if (stats.isDirectory()) {
                    totalSize += this.getDirSize(filePath);
                } else {
                    totalSize += stats.size;
                }
            }
        } catch (error) {
            console.error(`计算目录大小失败: ${dirPath}`, error);
        }
        return totalSize;
    }
    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = [
            'B',
            'KB',
            'MB',
            'GB'
        ];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    // 创建备份
    createBackup(filePath) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filePath);
            const baseName = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].basename(filePath, ext);
            const dir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(filePath);
            const backupPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dir, `${baseName}_backup_${timestamp}${ext}`);
            if (this.copyFile(filePath, backupPath)) {
                return backupPath;
            }
            return null;
        } catch (error) {
            console.error(`创建备份失败: ${filePath}`, error);
            return null;
        }
    }
    // 合并改写的章节为一个完整的小说文件
    mergeRewrittenChapters(novelTitle) {
        try {
            const rewrittenDir = this.getNovelRewrittenDir(novelTitle);
            const doneNovelsDir = this.getDoneNovelsDir();
            // 获取所有改写的章节文件
            const chapterFiles = this.listFiles(rewrittenDir, [
                '.txt'
            ]).filter((file)=>file.startsWith('chapter_') && file.includes('_rewritten')).sort((a, b)=>{
                // 提取章节号进行排序
                const aNum = parseInt(a.match(/chapter_(\d+)/)?.[1] || '0');
                const bNum = parseInt(b.match(/chapter_(\d+)/)?.[1] || '0');
                return aNum - bNum;
            });
            if (chapterFiles.length === 0) {
                return {
                    success: false,
                    error: '没有找到改写的章节文件'
                };
            }
            // 读取并合并所有章节
            let mergedContent = '';
            for (const chapterFile of chapterFiles){
                const chapterPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(rewrittenDir, chapterFile);
                const chapterContent = this.readFile(chapterPath);
                mergedContent += chapterContent + '\n\n';
            }
            // 生成合并后的文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
            const mergedFileName = `${this.sanitizeFilename(novelTitle)}_merged_${timestamp}.txt`;
            const mergedFilePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(doneNovelsDir, mergedFileName);
            // 写入合并后的文件
            this.writeFile(mergedFilePath, mergedContent.trim());
            return {
                success: true,
                filePath: mergedFilePath
            };
        } catch (error) {
            console.error(`合并章节失败: ${novelTitle}`, error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '未知错误'
            };
        }
    }
    // 清理旧备份文件
    cleanupBackups(dirPath, maxBackups = 5) {
        try {
            const files = this.listFiles(dirPath);
            const backupFiles = files.filter((file)=>file.includes('_backup_')).map((file)=>({
                    name: file,
                    path: __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file),
                    stats: this.getFileStats(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file))
                })).filter((item)=>item.stats !== null).sort((a, b)=>b.stats.mtime.getTime() - a.stats.mtime.getTime());
            // 删除超出数量限制的备份文件
            if (backupFiles.length > maxBackups) {
                const filesToDelete = backupFiles.slice(maxBackups);
                for (const file of filesToDelete){
                    this.deleteFile(file.path);
                }
            }
        } catch (error) {
            console.error(`清理备份文件失败: ${dirPath}`, error);
        }
    }
}
const fileManager = FileManager.getInstance();
}),
"[project]/src/lib/novel-parser.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getAvailableNovels",
    ()=>getAvailableNovels,
    "isNovelParsed",
    ()=>isNovelParsed,
    "parseChapterRange",
    ()=>parseChapterRange,
    "parseNovelFile",
    ()=>parseNovelFile,
    "reparseNovel",
    ()=>reparseNovel
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/file-manager.ts [app-route] (ecmascript)");
;
;
;
;
// 分卷/分集匹配模式（用于分栏，不作为章节）
const VOLUME_PATTERNS = [
    /^\s*(?:第[一二三四五六七八九十百千万\d]+[卷集部])\s*.*$/gmi,
    /^\s*(?:[卷集部][一二三四五六七八九十百千万\d]+)\s*.*$/gmi
];
// 章节匹配模式
const CHAPTER_PATTERNS = [
    /^\s*(?:第[一二三四五六七八九十百千万\d]+[章节回])\s*.*$/gmi,
    /^\s*(?:Chapter\s+\d+)\s*.*$/gmi
];
async function parseNovelFile(filePath) {
    const filename = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].basename(filePath);
    const title = filename.replace(/\.(txt|md)$/i, '');
    // 读取文件内容
    const content = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
    // 创建小说记录
    const novel = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].create({
        title,
        filename
    });
    // 解析章节
    const chapters = parseChapters(content, novel.id);
    // 批量创建章节记录
    const createdChapters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chapterDb"].createBatch(chapters);
    // 更新小说的章节数量
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].update(novel.id, {
        chapterCount: createdChapters.length
    });
    // 保存章节文件
    await saveChapterFiles(createdChapters);
    return {
        novel: {
            ...novel,
            chapterCount: createdChapters.length
        },
        chapters: createdChapters
    };
}
// 解析章节内容
function parseChapters(content, novelId) {
    const chapters = [];
    // 首先识别分卷/分集标记
    const volumeMatches = findVolumeMarkers(content);
    // 然后在每个分卷内或整个文本中查找章节
    if (volumeMatches.length > 0) {
        // 有分卷的情况
        console.log(`Found ${volumeMatches.length} volumes`);
        let chapterNumber = 1;
        for(let i = 0; i < volumeMatches.length; i++){
            const volumeStart = volumeMatches[i].index;
            const volumeEnd = i + 1 < volumeMatches.length ? volumeMatches[i + 1].index : content.length;
            const volumeContent = content.slice(volumeStart, volumeEnd);
            // 在分卷内查找章节
            const volumeChapters = parseChaptersInVolume(volumeContent, novelId, chapterNumber, volumeMatches[i].title);
            chapters.push(...volumeChapters);
            chapterNumber += volumeChapters.length;
        }
    } else {
        // 没有分卷，直接解析章节
        const directChapters = parseChaptersInVolume(content, novelId, 1);
        chapters.push(...directChapters);
    }
    console.log(`Successfully parsed ${chapters.length} chapters`);
    return chapters;
}
// 查找分卷标记
function findVolumeMarkers(content) {
    const volumeMarkers = [];
    for (const pattern of VOLUME_PATTERNS){
        const matches = Array.from(content.matchAll(pattern));
        for (const match of matches){
            volumeMarkers.push({
                index: match.index,
                title: extractChapterTitle(match[0])
            });
        }
    }
    // 按位置排序
    return volumeMarkers.sort((a, b)=>a.index - b.index);
}
// 在指定内容中解析章节（重构版）
function parseChaptersInVolume(content, novelId, startChapterNumber, volumeTitle) {
    const chapters = [];
    let chapterNumber = startChapterNumber;
    // 1. 寻找最佳匹配模式
    let bestPattern = null;
    let bestMatchesCount = -1;
    for (const pattern of CHAPTER_PATTERNS){
        const matches = content.match(pattern); // 使用 match 而不是 matchAll 来计数
        const matchCount = matches ? matches.length : 0;
        if (matchCount > bestMatchesCount) {
            bestMatchesCount = matchCount;
            bestPattern = pattern;
        }
    }
    // 2. 如果没有找到任何章节标记，将整个内容作为一章
    if (!bestPattern || bestMatchesCount === 0) {
        const trimmedContent = content.trim();
        if (trimmedContent.length > 100) {
            chapters.push({
                novelId,
                chapterNumber: chapterNumber,
                title: volumeTitle || '全文',
                content: trimmedContent,
                filename: `chapter_${chapterNumber}.txt`
            });
        }
        return chapters;
    }
    // 3. 使用 split 进行分割 (关键改动)
    // 创建一个带捕获组的新正则表达式，以便 split 保留分隔符
    const splitPattern = new RegExp(`(${bestPattern.source})`, 'gmi');
    const parts = content.split(splitPattern);
    // parts 数组的结构会是: [前言部分, 标题1, 内容1, 标题2, 内容2, ...]
    let currentContent = '';
    // 处理可能存在的前言/序章（parts[0]）
    const prologue = parts[0]?.trim();
    if (prologue && prologue.length > 100) {
        chapters.push({
            novelId,
            chapterNumber: chapterNumber,
            // 你可以给它一个固定的名字，或者尝试从内容中提取
            title: '序章',
            content: prologue,
            filename: `chapter_${chapterNumber}.txt`
        });
        chapterNumber++;
    }
    console.log(parts.map((v, i)=>i + v));
    // 4. 循环处理分割后的部分
    for(let i = 1; i < parts.length; i += 2){
        const titlePart = parts[i];
        const contentPart = parts[i + 1] || ''; // 后面的内容部分
        if (!titlePart) continue;
        const trimmedContent = (titlePart + contentPart).trim();
        if (trimmedContent.length > 100) {
            chapters.push({
                novelId,
                chapterNumber: chapterNumber,
                title: extractChapterTitle(titlePart),
                content: trimmedContent,
                filename: `chapter_${chapterNumber}.txt`
            });
            chapterNumber++;
        }
    }
    return chapters;
}
// 提取章节标题
function extractChapterTitle(chapterText) {
    const lines = chapterText.trim().split('\n');
    const firstLine = lines[0].trim();
    // 如果第一行看起来像标题，使用它
    if (firstLine.length < 100 && firstLine.length > 0) {
        return firstLine;
    }
    // 否则尝试从前几行中找到标题
    for(let i = 0; i < Math.min(3, lines.length); i++){
        const line = lines[i].trim();
        if (line.length > 0 && line.length < 100) {
            return line;
        }
    }
    return '未命名章节';
}
// 保存章节文件到chapters目录
async function saveChapterFiles(chapters) {
    // 为每个小说创建子目录
    const novelIds = [
        ...new Set(chapters.map((ch)=>ch.novelId))
    ];
    for (const novelId of novelIds){
        const novel = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getById(novelId);
        if (!novel) continue;
        const novelDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelChaptersDir(novel.title);
        // 保存该小说的所有章节
        const novelChapters = chapters.filter((ch)=>ch.novelId === novelId);
        for (const chapter of novelChapters){
            const chapterPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(novelDir, chapter.filename);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].writeFile(chapterPath, chapter.content);
        }
    }
}
function getAvailableNovels() {
    const novelsDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelsDir();
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].listFiles(novelsDir, [
        '.txt',
        '.md'
    ]);
}
function isNovelParsed(filename) {
    const novels = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getAll();
    return novels.some((novel)=>novel.filename === filename);
}
async function reparseNovel(filename) {
    const novelsDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelsDir();
    const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(novelsDir, filename);
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].fileExists(filePath)) {
        return null;
    }
    // 删除旧的小说和章节数据
    const existingNovels = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getAll();
    const existingNovel = existingNovels.find((novel)=>novel.filename === filename);
    if (existingNovel) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chapterDb"].deleteByNovelId(existingNovel.id);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].delete(existingNovel.id);
    }
    // 重新解析
    return await parseNovelFile(filePath);
}
function parseChapterRange(rangeStr, maxChapter) {
    const chapters = [];
    const parts = rangeStr.split(',').map((part)=>part.trim());
    for (const part of parts){
        if (part.includes('-')) {
            // 范围格式 (例如: "1-5")
            const [start, end] = part.split('-').map((num)=>parseInt(num.trim()));
            if (!isNaN(start) && !isNaN(end) && start <= end) {
                for(let i = start; i <= Math.min(end, maxChapter); i++){
                    if (i > 0 && !chapters.includes(i)) {
                        chapters.push(i);
                    }
                }
            }
        } else {
            // 单个章节
            const chapterNum = parseInt(part);
            if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {
                chapters.push(chapterNum);
            }
        }
    }
    return chapters.sort((a, b)=>a - b);
}
}),
"[project]/src/app/api/rewrite/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/context-utils.ts [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$novel$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/novel-parser.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/file-manager.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
;
;
async function POST(request) {
    try {
        const { novelId, chapterRange, rules, model, concurrency } = await request.json();
        if (!novelId || !chapterRange || !rules) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '参数不完整'
            }, {
                status: 400
            });
        }
        // 验证模型和并发数参数
        const validModels = [
            'gemini-2.5-flash-lite',
            'gemini-2.5-flash',
            'gemini-1.5-pro'
        ];
        const selectedModel = model && validModels.includes(model) ? model : 'gemini-2.5-flash-lite';
        const selectedConcurrency = concurrency && concurrency >= 1 && concurrency <= 10 ? concurrency : 3;
        // 获取小说信息
        const novel = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getById(novelId);
        if (!novel) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '小说不存在'
            }, {
                status: 404
            });
        }
        // 获取所有章节
        const allChapters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chapterDb"].getByNovelId(novelId);
        if (allChapters.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '该小说没有章节'
            }, {
                status: 404
            });
        }
        // 解析章节范围
        const chapterNumbers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$novel$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseChapterRange"])(chapterRange, allChapters.length);
        if (chapterNumbers.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '无效的章节范围'
            }, {
                status: 400
            });
        }
        // 获取要改写的章节
        const chaptersToRewrite = allChapters.filter((chapter)=>chapterNumbers.includes(chapter.chapterNumber));
        if (chaptersToRewrite.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '没有找到指定的章节'
            }, {
                status: 404
            });
        }
        // 创建改写任务
        const job = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].create({
            novelId,
            chapters: chapterNumbers,
            ruleId: 'custom',
            status: 'pending',
            progress: 0,
            details: {
                totalChapters: chaptersToRewrite.length,
                completedChapters: 0,
                failedChapters: 0,
                totalTokensUsed: 0,
                totalProcessingTime: 0,
                averageTimePerChapter: 0,
                apiKeyStats: [],
                chapterResults: [],
                model: selectedModel,
                concurrency: selectedConcurrency
            }
        });
        // 异步执行改写任务
        executeRewriteJob(job.id, chaptersToRewrite, rules, novel.title, selectedModel, selectedConcurrency);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                jobId: job.id,
                chaptersCount: chaptersToRewrite.length,
                message: '改写任务已创建，正在处理中...'
            }
        });
    } catch (error) {
        console.error('创建改写任务失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '创建改写任务失败'
        }, {
            status: 500
        });
    }
}
// 异步执行改写任务 - 支持实时写入和详细进度
async function executeRewriteJob(jobId, chapters, rules, novelTitle, model = 'gemini-2.5-flash-lite', concurrency = 3) {
    const startTime = Date.now();
    try {
        // 更新任务状态为处理中
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
            status: 'processing',
            progress: 0,
            details: {
                totalChapters: chapters.length,
                completedChapters: 0,
                failedChapters: 0,
                totalTokensUsed: 0,
                totalProcessingTime: 0,
                averageTimePerChapter: 0,
                apiKeyStats: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId)?.details?.apiKeyStats || [],
                chapterResults: [],
                model: model,
                concurrency: concurrency
            }
        });
        // 准备章节数据
        const chaptersData = chapters.map((chapter)=>({
                content: chapter.content,
                title: chapter.title,
                number: chapter.chapterNumber
            }));
        // 创建输出目录
        const outputDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelRewrittenDir(novelTitle);
        // 执行改写（降低并发数以避免429错误，启用失败恢复）
        const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["rewriteChaptersWithContext"])(chapters[0].novelId, chaptersData, rules, // 进度回调 - 更新详细信息
        (progress, _currentChapter, details)=>{
            const currentJob = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId);
            if (currentJob && details) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
                    progress: Math.round(progress),
                    details: {
                        totalChapters: currentJob.details?.totalChapters || chapters.length,
                        completedChapters: details.completed,
                        failedChapters: currentJob.details?.failedChapters || 0,
                        totalTokensUsed: details.totalTokensUsed,
                        totalProcessingTime: details.totalTime,
                        averageTimePerChapter: details.averageTimePerChapter,
                        apiKeyStats: details.apiKeyStats,
                        chapterResults: currentJob.details?.chapterResults || [],
                        model: model,
                        concurrency: concurrency
                    }
                });
            }
        }, // 章节完成回调 - 实时写入
        (chapterIndex, result)=>{
            if (result.success) {
                // 立即写入完成的章节
                const chapter = chapters[chapterIndex];
                const filename = `chapter_${chapter.chapterNumber}_rewritten.txt`;
                const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(outputDir, filename);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].writeFile(filePath, result.content);
            }
            // 更新章节结果到数据库
            const currentJob = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId);
            if (currentJob?.details && result) {
                const chapterResults = [
                    ...currentJob.details.chapterResults || []
                ];
                chapterResults[chapterIndex] = {
                    chapterNumber: result.details?.chapterNumber || chapterIndex + 1,
                    chapterTitle: result.details?.chapterTitle || `第${chapterIndex + 1}章`,
                    success: result.success,
                    error: result.error,
                    apiKeyUsed: result.details?.apiKeyUsed,
                    tokensUsed: result.details?.tokensUsed,
                    processingTime: result.details?.processingTime,
                    completedAt: new Date().toISOString()
                };
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
                    details: {
                        ...currentJob.details,
                        chapterResults,
                        failedChapters: chapterResults.filter((r)=>r && !r.success).length
                    }
                });
            }
        }, concurrency, model, true // 启用失败恢复机制
        );
        // 统计结果
        let successCount = 0;
        let totalTokensUsed = 0;
        const resultSummary = [];
        for(let i = 0; i < results.length; i++){
            const result = results[i];
            const chapter = chapters[i];
            // 检查 result 是否为 null 或 undefined
            if (result && result.success) {
                successCount++;
            }
            if (result?.details?.tokensUsed) {
                totalTokensUsed += result.details.tokensUsed;
            }
            resultSummary.push({
                chapterNumber: chapter.chapterNumber,
                chapterTitle: chapter.title,
                success: result ? result.success : false,
                error: result?.error || '处理失败',
                apiKeyUsed: result?.details?.apiKeyUsed,
                tokensUsed: result?.details?.tokensUsed,
                processingTime: result?.details?.processingTime
            });
        }
        // 保存详细的结果摘要
        const summaryPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(outputDir, 'rewrite_summary.json');
        const totalProcessingTime = Date.now() - startTime;
        const summaryData = JSON.stringify({
            jobId,
            novelTitle,
            rules,
            totalChapters: chapters.length,
            successCount,
            failedCount: chapters.length - successCount,
            totalTokensUsed,
            totalProcessingTime,
            averageTimePerChapter: totalProcessingTime / chapters.length,
            results: resultSummary,
            completedAt: new Date().toISOString(),
            model: model,
            concurrency: concurrency
        }, null, 2);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].writeFile(summaryPath, summaryData);
        // 更新任务状态为完成
        const currentJob = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId);
        const finalStatus = successCount > 0 ? 'completed' : 'failed';
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
            status: finalStatus,
            progress: 100,
            result: `成功改写 ${successCount}/${chapters.length} 章节，结果保存在: ${outputDir}`,
            details: {
                totalChapters: chapters.length,
                completedChapters: successCount,
                failedChapters: chapters.length - successCount,
                totalTokensUsed,
                totalProcessingTime,
                averageTimePerChapter: chapters.length > 0 ? totalProcessingTime / chapters.length : 0,
                apiKeyStats: currentJob?.details?.apiKeyStats || [],
                chapterResults: resultSummary,
                model: model,
                concurrency: concurrency
            }
        });
    } catch (error) {
        console.error('执行改写任务失败:', error);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
            status: 'failed',
            result: `改写失败: ${error instanceof Error ? error.message : '未知错误'}`
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__9f7c6129._.js.map